
import React from 'react';
import { useAuth } from '@/hooks/auth/useAuth';
import Navbar from '@/components/navigation/Navbar';
import Hero from '@/components/Hero';
import Footer from '@/components/Footer';
import DashboardQuickAccess from '@/components/navigation/DashboardQuickAccess';

const Index = () => {
  const { user, loading } = useAuth();

  console.log('Index page rendering - with auth hook. User:', user, 'Loading:', loading);

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white overflow-hidden">
      <Navbar />
      <main>
        <Hero />

        {/* Dashboard Quick Access - Only show for authenticated users */}
        {user && (
          <section className="py-16 bg-gradient-to-br from-gray-50 to-white">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
              <DashboardQuickAccess />
            </div>
          </section>
        )}

        {/* Status Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-8">Application Status</h2>
            {user ? (
              <div className="space-y-4">
                <p className="text-xl text-gray-600">
                  Welcome back, {user.firstName} {user.lastName}!
                </p>
                <p className="text-gray-500">Role: {user.role}</p>
                <div className="bg-green-100 p-4 rounded-lg max-w-md mx-auto">
                  <p className="text-green-800">✅ Authentication is working correctly!</p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <p className="text-xl text-gray-600">Please sign in to access your dashboard</p>
                <div className="bg-yellow-100 p-4 rounded-lg max-w-md mx-auto">
                  <p className="text-yellow-800">⚠️ Not authenticated</p>
                </div>
              </div>
            )}

            <div className="space-x-4 mt-8">
              <a href="/auth" className="inline-block bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors">
                Go to Auth
              </a>
              <a href="/properties" className="inline-block bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                View Properties
              </a>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default Index;
