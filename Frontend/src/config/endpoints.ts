
// API endpoint configurations
import { API_CONFIG } from './environment';

const API_BASE_URL = API_CONFIG.BASE_URL;

export const PROPERTY_ENDPOINTS = {
  // Core properties
  GET_PROPERTIES: `${API_BASE_URL}/properties`,
  GET_PROPERTY_BY_ID: (id: string) => `${API_BASE_URL}/properties/${id}`,
  CREATE_PROPERTY: `${API_BASE_URL}/properties`,
  UPDATE_PROPERTY: (id: string) => `${API_BASE_URL}/properties/${id}`,
  DELETE_PROPERTY: (id: string) => `${API_BASE_URL}/properties/${id}`,

  // Property Images
  GET_IMAGES: (propertyId: string) => `${API_BASE_URL}/properties/${propertyId}/images/all-images`,
  UPLOAD_IMAGE: (propertyId: string) => `${API_BASE_URL}/properties/${propertyId}/images/upload`,
  DELETE_IMAGE: (propertyId: string, imageId: string) => `${API_BASE_URL}/properties/${propertyId}/images/${imageId}`,
  REORDER_IMAGES: (propertyId: string) => `${API_BASE_URL}/properties/${propertyId}/images/reorder`,

  // Property Analytics
  GET_PROPERTY_ANALYTICS: (propertyId: string) => `${API_BASE_URL}/properties/${propertyId}/analytics`,
  GET_PROPERTY_MARKET_DATA: `${API_BASE_URL}/properties/market-data`,
  GET_PROPERTY_RECOMMENDATIONS: (userId: string) => `${API_BASE_URL}/properties/${userId}/recommendations`,

  // Saved Searches
  SAVE_SEARCH: `${API_BASE_URL}/saved-searches`,
  GET_SAVED_SEARCHES: (userId: string) => `${API_BASE_URL}/saved-searches/user/${userId}`,
  GET_SAVED_SEARCH_BY_ID: (id: string) => `${API_BASE_URL}/saved-searches/${id}`,
  UPDATE_SAVED_SEARCH: (id: string) => `${API_BASE_URL}/saved-searches/${id}`,
  DELETE_SAVED_SEARCH: (id: string) => `${API_BASE_URL}/saved-searches/${id}`,
};

export const AUTH_ENDPOINTS = {
  LOGIN: `${API_BASE_URL}/auth/login`,
  REGISTER: `${API_BASE_URL}/auth/register`,
  REFRESH: `${API_BASE_URL}/auth/refresh`,
  LOGOUT: `${API_BASE_URL}/auth/logout`,
};

export const USER_ENDPOINTS = {
  GET_PROFILE: `${API_BASE_URL}/users/profile`,
  UPDATE_PROFILE: `${API_BASE_URL}/users/profile`,
};
