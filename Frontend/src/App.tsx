import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Toaster } from '@/components/ui/toaster';
import { Toaster as Sonner } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import Index from './pages/Index';
import Properties from './pages/Properties';
import Auth from './pages/Auth';
import Contact from './pages/Contact';
import UserProfile from './pages/UserProfile';
import Search from './pages/Search';
import Messages from './pages/Messages';
import PropertyManagement from './pages/PropertyManagement';
import TenantPortal from './pages/TenantPortal';
import LandlordPortal from './pages/LandlordPortal';
import AgentDashboard from './pages/AgentDashboard';
import EnhancedAgentDashboard from './pages/EnhancedAgentDashboard';
import AdminDashboard from './pages/AdminDashboard';
import AdminSeedData from './pages/AdminSeedData';
import AdvancedFeatures from './pages/AdvancedFeatures';
import AdvancedFeaturesTest from './pages/AdvancedFeaturesTest';
import PaymentDashboard from './pages/PaymentDashboard';
import ScalingOptimization from './pages/ScalingOptimization';
import AdvancedBusinessLogic from './pages/AdvancedBusinessLogic';
import RentalApplication from './pages/RentalApplication';
import VerificationStatus from './pages/VerificationStatus';
import Escrow from './pages/Escrow';
import PaymentCallback from './pages/PaymentCallback';
import PaymentDebug from './pages/PaymentDebug';
import ReceiptDemo from './pages/ReceiptDemo';
import PaymentTest from './pages/PaymentTest';
import AppStatus from './pages/AppStatus';
import CreateAlert from './pages/CreateAlert';
import ContactAgent from './pages/ContactAgent';
import MaintenanceDashboard from './pages/MaintenanceDashboard';
import AnalyticsDashboardPage from './pages/AnalyticsDashboardPage';
import CommunicationDashboardPage from './pages/CommunicationDashboardPage';
import PerformanceDashboardPage from './pages/PerformanceDashboardPage';
import SecurityDashboardPage from './pages/SecurityDashboardPage';
import NotFound from './pages/NotFound';
import ErrorBoundary from './components/error/ErrorBoundary';
import PageErrorBoundary from './components/error/PageErrorBoundary';

// Simple test component
const SimpleHome = () => (
  <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
    <h1 style={{ color: '#333', marginBottom: '20px' }}>
      PHCityRent - Port Harcourt Real Estate Platform
    </h1>
    <p style={{ color: '#666', fontSize: '16px', lineHeight: '1.6' }}>
      Welcome to PHCityRent! This is a simplified version to test the React application.
    </p>
    <div
      style={{
        marginTop: '20px',
        padding: '15px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
      }}
    >
      <h2 style={{ color: '#28a745', marginBottom: '10px' }}>✅ Application Status</h2>
      <ul style={{ color: '#666' }}>
        <li>✅ React is working</li>
        <li>✅ Routing is functional</li>
        <li>✅ UI components are loading</li>
        <li>✅ Development server is running</li>
      </ul>
    </div>
    <div style={{ marginTop: '20px' }}>
      <button
        style={{
          padding: '10px 20px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '16px',
        }}
        onClick={() => alert('PHCityRent is working!')}
      >
        Test Interaction
      </button>
    </div>
  </div>
);

function App() {
  return (
    <ErrorBoundary level="critical" showDetails={process.env.NODE_ENV === 'development'}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <Routes>
          <Route
            path="/"
            element={
              <PageErrorBoundary pageName="Home">
                <Index />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/properties"
            element={
              <PageErrorBoundary pageName="Properties">
                <Properties />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/auth"
            element={
              <PageErrorBoundary pageName="Authentication">
                <Auth />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/contact"
            element={
              <PageErrorBoundary pageName="Contact">
                <Contact />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/search"
            element={
              <PageErrorBoundary pageName="Search">
                <Search />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/profile"
            element={
              <PageErrorBoundary pageName="User Profile">
                <UserProfile />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/messages"
            element={
              <PageErrorBoundary pageName="Messages">
                <Messages />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/property-management"
            element={
              <PageErrorBoundary pageName="Property Management">
                <PropertyManagement />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/tenant-portal"
            element={
              <PageErrorBoundary pageName="Tenant Portal">
                <TenantPortal />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/landlord-portal"
            element={
              <PageErrorBoundary pageName="Landlord Portal">
                <LandlordPortal />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/agent-dashboard"
            element={
              <PageErrorBoundary pageName="Agent Dashboard">
                <AgentDashboard />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/enhanced-agent-dashboard"
            element={
              <PageErrorBoundary pageName="Enhanced Agent Dashboard">
                <EnhancedAgentDashboard />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/admin"
            element={
              <PageErrorBoundary pageName="Admin Dashboard">
                <AdminDashboard />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/admin/seed-data"
            element={
              <PageErrorBoundary pageName="Admin Seed Data">
                <AdminSeedData />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/advanced-features"
            element={
              <PageErrorBoundary pageName="Advanced Features">
                <AdvancedFeatures />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/advanced-features-test"
            element={
              <PageErrorBoundary pageName="Advanced Features Test">
                <AdvancedFeaturesTest />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/payment-dashboard"
            element={
              <PageErrorBoundary pageName="Payment Dashboard">
                <PaymentDashboard />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/scaling-optimization"
            element={
              <PageErrorBoundary pageName="Scaling Optimization">
                <ScalingOptimization />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/advanced-business-logic"
            element={
              <PageErrorBoundary pageName="Advanced Business Logic">
                <AdvancedBusinessLogic />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/rental-application"
            element={
              <PageErrorBoundary pageName="Rental Application">
                <RentalApplication />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/verification-status"
            element={
              <PageErrorBoundary pageName="Verification Status">
                <VerificationStatus />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/escrow"
            element={
              <PageErrorBoundary pageName="Escrow">
                <Escrow />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/payment-callback"
            element={
              <PageErrorBoundary pageName="Payment Callback">
                <PaymentCallback />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/payment-debug"
            element={
              <PageErrorBoundary pageName="Payment Debug">
                <PaymentDebug />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/receipt-demo"
            element={
              <PageErrorBoundary pageName="Receipt Demo">
                <ReceiptDemo />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/payment-test"
            element={
              <PageErrorBoundary pageName="Payment Test">
                <PaymentTest />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/app-status"
            element={
              <PageErrorBoundary pageName="App Status">
                <AppStatus />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/create-alert"
            element={
              <PageErrorBoundary pageName="Create Alert">
                <CreateAlert />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/contact-agent"
            element={
              <PageErrorBoundary pageName="Contact Agent">
                <ContactAgent />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/maintenance-dashboard"
            element={
              <PageErrorBoundary pageName="Maintenance Dashboard">
                <MaintenanceDashboard />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/analytics-dashboard"
            element={
              <PageErrorBoundary pageName="Analytics Dashboard">
                <AnalyticsDashboardPage />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/communication-dashboard"
            element={
              <PageErrorBoundary pageName="Communication Dashboard">
                <CommunicationDashboardPage />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/performance-dashboard"
            element={
              <PageErrorBoundary pageName="Performance Dashboard">
                <PerformanceDashboardPage />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/security-dashboard"
            element={
              <PageErrorBoundary pageName="Security Dashboard">
                <SecurityDashboardPage />
              </PageErrorBoundary>
            }
          />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </TooltipProvider>
    </ErrorBoundary>
  );
}

export default App;
