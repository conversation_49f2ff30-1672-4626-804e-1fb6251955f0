
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { propertyService } from '@/services/properties/propertyService';
import { Property } from '@/types/property';

// Helper to extract error message
const getErrorMessage = (error: any): string => {
  if (error instanceof Error) return error.message;
  if (error && typeof error === 'object' && 'message' in error) return (error as any).message;
  return 'An unexpected error occurred';
};

/**
 * Hook for fetching all properties with optional search filters.
 */
export const useProperties = (searchParams?: Record<string, any>) => {
  return useQuery<Property[], Error>({
    queryKey: ['properties', searchParams],
    queryFn: () => propertyService.getProperties(searchParams),
  });
};

/**
 * Hook for fetching a single property by ID.
 */
export const useProperty = (propertyId: string) => {
  return useQuery<Property, Error>({
    queryKey: ['property', propertyId],
    queryFn: () => propertyService.getPropertyById(propertyId),
    enabled: !!propertyId,
  });
};

/**
 * Hook for creating a new property.
 */
export const useCreateProperty = () => {
  const queryClient = useQueryClient();

  return useMutation<Property, Error, Partial<Property>>({
    mutationFn: (propertyData) => propertyService.createProperty(propertyData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
    },
    onError: (error) => {
      console.error('Failed to create property:', getErrorMessage(error));
    },
  });
};

/**
 * Hook for updating an existing property.
 */
export const useUpdateProperty = () => {
  const queryClient = useQueryClient();

  return useMutation<Property, Error, { id: string; data: Partial<Property> }>({
    mutationFn: ({ id, data }) => propertyService.updateProperty(id, data),
    onSuccess: (updatedProperty) => {
      queryClient.invalidateQueries({ queryKey: ['property', updatedProperty.id] });
      queryClient.invalidateQueries({ queryKey: ['properties'] });
    },
    onError: (error) => {
      console.error('Failed to update property:', getErrorMessage(error));
    },
  });
};

/**
 * Hook for deleting a property.
 */
export const useDeleteProperty = () => {
  const queryClient = useQueryClient();

  return useMutation<void, Error, string>({
    mutationFn: (propertyId) => propertyService.deleteProperty(propertyId),
    onSuccess: (_, propertyId) => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
      queryClient.removeQueries({ queryKey: ['property', propertyId] });
    },
    onError: (error) => {
      console.error('Failed to delete property:', getErrorMessage(error));
    },
  });
};

/**
 * Hook for fetching properties owned by the current user.
 */
export const useMyProperties = () => {
  return useQuery({
    queryKey: ['my-properties'],
    queryFn: () => propertyService.getMyProperties(),
  });
};

/**
 * Hook for fetching property statistics.
 */
export const usePropertyStats = () => {
  return useQuery({
    queryKey: ['property-stats'],
    queryFn: () => propertyService.getPropertyStats(),
  });
};
