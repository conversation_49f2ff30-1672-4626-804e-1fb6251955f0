import apiClient, { ApiResponse } from '@/services/apiClient';
import { HTTP_STATUS } from '@/config/api';
import { PROPERTY_ENDPOINTS } from '@/config/endpoints/properties';
import { errorHandler } from '@/services/errorHandler';

import {
  Property,
  PropertyWithAnalytics,
  PropertyImage,
  PropertyVerificationStep,
  PropertyDocument,
  PropertyInspection,
  PropertyAnalytics,
  SearchFilters,
  SavedSearch,
  PropertyAlert,
  VirtualTour,
  CreatePropertyRequest,
  UpdatePropertyRequest,
  ImageUploadRequest,
  ImageUploadProgress,
  PropertySearchResult,
  PropertyMarketData,
  PropertyRecommendation,
  PropertyServiceError
} from '@/types/property';

/**
 * Comprehensive Property Management Service
 * Handles core property-related CRUD operations and searching.
 * 
 */
export class PropertyService {
  private static instance: PropertyService;

  public static getInstance(): PropertyService {
    if (!PropertyService.instance) {
      PropertyService.instance = new PropertyService();
    }
    return PropertyService.instance;
  }

  // Helper for consistent error handling
  private handleServiceError(
    error: any, action: string, defaultMessage: string,
    defaultStatus: number
  ): PropertyServiceError {
    if (error instanceof PropertyServiceError) return error;
    errorHandler.handleError({
      message: error.message || defaultMessage,
      code: error.statusCode || defaultStatus,
      status: error.statusCode || defaultStatus,
      severity: 'high', // Adjust severity as needed
      category: 'property',
      context: { component: 'PropertyService', action },
    });
    return new PropertyServiceError(
      error.message || defaultMessage, 'UNKNOWN_ERROR', error
    );
  }

  // =====================================================
  // PROPERTY CRUD OPERATIONS
  // =====================================================

   /**
   * Creates a new property.
   * @param data The property data to create.
   * @returns The created property.
   */
    async createProperty(propertyData: CreatePropertyRequest): Promise<Property> {
      try {
        const response: ApiResponse<Property> = await apiClient.post(
          PROPERTY_ENDPOINTS.CREATE,
          propertyData
        );
        return response.data;
      } catch (error) {
        throw this.handleServiceError(
          error, 'Create Property',
          'Failed to create property',
          HTTP_STATUS.BAD_REQUEST);
      }
    }

  /**
   * Get property by ID with analytics
   */
  async getProperty(propertyId: string): Promise<PropertyWithAnalytics> {
    try {
      const response: ApiResponse<PropertyWithAnalytics> = await apiClient.get(
        PROPERTY_ENDPOINTS.GET_PROPERTY_BY_ID(propertyId)
      );
      return response.data;
    } catch (error) {
      throw this.handleServiceError(
        error, 'Get Property by ID',
        'Failed to fetch property', HTTP_STATUS.NOT_FOUND
      );
    }
  }

  /**
   * Get properties with search and filtering
   */
  async getProperties(
    filters: SearchFilters = {},
    page = 1, pageSize = 20
  ): Promise<PropertySearchResult> {
    try {
      const queryParams = new URLSearchParams();
      
      // Add filters to query params
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              // Handle array values (like amenities)
              value.forEach(item => queryParams.append(key, item));
            } else {
              queryParams.append(key, value.toString());
            }
          }
        });
      }
      
      // Add pagination
      queryParams.append('page', page.toString());
      queryParams.append('limit', pageSize.toString());

      const response: ApiResponse<PropertySearchResult> = await apiClient.get(
        `${PROPERTY_ENDPOINTS.SEARCH}?${queryParams.toString()}`
      );
      return response.data;
    } catch (error) {
      throw this.handleServiceError(
        error, 
        'Get Properties', 
        'Failed to fetch properties', HTTP_STATUS.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Get properties for the current user
   */
  async getMyProperties(
    filters: SearchFilters = {},
    page = 1, pageSize = 20
  ): Promise<PropertySearchResult> {
    try {
      const queryParams = new URLSearchParams();
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            queryParams.append(key, value.toString());
          }
        });
      }
      queryParams.append('page', page.toString());
      queryParams.append('limit', pageSize.toString());

      const response: ApiResponse<PropertySearchResult> = await apiClient.get(
        `${PROPERTY_ENDPOINTS.MY_PROPERTIES}?${queryParams.toString()}`
      );
      return response.data;
    } catch (error) {
      throw this.handleServiceError(
        error, 'Get My Properties',
        'Failed to fetch user properties',
        HTTP_STATUS.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update property
   */
  async updateProperty(
    propertyId: string,
    updates: UpdatePropertyRequest
  ): Promise<Property> {
    try {
      const response: ApiResponse<Property> = await apiClient.patch( // Using PATCH for partial updates
        PROPERTY_ENDPOINTS.UPDATE_PROPERTY(propertyId),
        updates
      );
      // Log activity
      return response.data;
    } catch (error) {
      throw this.handleServiceError(
        error, 'Update Property',
        'Failed to update property',
        HTTP_STATUS.BAD_REQUEST
      );
    }
  }

  /**
   * Get property statistics for the current user/agent.
   */
  async getPropertyStats(): Promise<PropertyAnalytics> {
    try {
      const response: ApiResponse<PropertyAnalytics> = await apiClient.get(
        PROPERTY_ENDPOINTS.STATS
      );
      return response.data;
    } catch (error) {
      throw this.handleServiceError(
        error, 'Get Property Stats',
        'Failed to fetch property stats',
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Delete property
   */
  async deleteProperty(propertyId: string): Promise<void> {
    try {
      await apiClient.delete(PROPERTY_ENDPOINTS.DELETE_PROPERTY(propertyId));
      // Log activity
    } catch (error) {
      throw this.handleServiceError(
        error, 'Delete Property', 'Failed to delete property', 
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      );
    }
  }
}

// Export singleton instance
export const propertyService = PropertyService.getInstance();
