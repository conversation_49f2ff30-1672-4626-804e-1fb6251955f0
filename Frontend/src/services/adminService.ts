import apiClient, { ApiResponse } from './apiClient';
import { HTTP_STATUS } from '@/config/api';
import { ADMIN_ENDPOINTS } from '@/config/endpoints/admin';

export interface Application {
  id: string;
  agent_id: string;
  full_name: string;
  whatsapp_number: string;
  email: string;
  status: 'pending_review' | 'approved' | 'rejected';
  created_at: string;
  operating_areas: string[];
  residential_address: string;
  is_registered_business: boolean;
  reviewer_notes?: string;
  next_action?: string;
  referee_verifications?: {
    status: string;
    referee_full_name: string;
    referee_whatsapp_number: string;
    referee_role: string;
  }[];
}

export interface AdminStats {
  users: {
    total: number;
    activeAgents: number;
  };
  properties: {
    total: number;
    available: number;
    rented: number;
    occupancyRate: number;
  };
  payments: {
    total: number;
    totalRevenue: number;
    monthlyRevenue: number;
  };
}

export interface AnalyticsData {
  applications: Application[];
  analytics: any;
}

export interface PerformanceData {
  metrics: {
    applications_reviewed: number;
    average_review_time_hours: number;
    approval_rate: number;
  };
  dailyActivity: any[];
  adminList: any[];
}

class AdminService {
  // Applications
  async getApplications(params?: {
    status?: string;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{ data: Application[]; total: number }> {

    const response: ApiResponse = await apiClient.get('/admin/applications', {
      params
    });
    return response.data;
  }

  async updateApplicationStatus(
    applicationId: string,
    status: 'approved' | 'rejected',
    notes?: string
  ): Promise<Application> {
    const response: ApiResponse = await apiClient.patch(`/admin/applications/${applicationId}/status`, {
      status,
      reviewer_notes: notes
    });
    return response.data;
  }

  async bulkUpdateApplications(
    applicationIds: string[],
    action: string,
    data?: any
  ): Promise<{ success: boolean; affectedCount: number }> {
    const response: ApiResponse = await apiClient.post('/admin/applications/bulk-action', {
      ids: applicationIds,
      action,
      data
    });
    return response.data;
  }

  // Analytics
  async getAnalyticsExport(params: {
    dateRange: string;
    exportType: string;
  }): Promise<AnalyticsData> {
    const response: ApiResponse = await apiClient.get('/admin/analytics/export', {
      params
    });
    return response.data;
  }

  async getPerformanceData(params: {
    selectedAdmin?: string;
    dateRange: string;
  }): Promise<PerformanceData> {
    const response: ApiResponse = await apiClient.get('/admin/analytics/performance', {
      params
    });
    return response.data;
  }

  // Dashboard Stats
  async getDashboardStats(): Promise<AdminStats> {
    const response: ApiResponse = await apiClient.get('/admin/dashboard/stats');
    return response.data;
  }

  async getSystemOverview(): Promise<any> {
    try {
      const response = await apiClient.get(
        ADMIN_ENDPOINTS.GET_SYSTEM_OVERVIEW
      )
      return response.data;
    } catch (error) {
      console.log('Error fetching system overview', error);
      throw error;
    }
  }

  // Users Management
  async getAllUsers(params?: {
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{ data: any[]; total: number }> {
    try {
      const response: ApiResponse = await apiClient.get(
        ADMIN_ENDPOINTS.GET_ALL_USERS
      );
      return response.data;
    } catch (error) {
      console.log('Failed to fetch all users', error);
      throw error;
    }
  }

  async suspendUser(userId: string): Promise<any> {
    try {
      const response = await apiClient.patch(
        ADMIN_ENDPOINTS.SUSPEND_USER(userId)
      );
      return response.data;
    } catch (error) {
      
    }
  }

  async activateUser(userId: string): Promise<any> {
    try {
      const response = await apiClient.patch(
        ADMIN_ENDPOINTS.ACTIVATE_USER(userId)
      );
      return response.data;
    } catch (error) {
      console.log('Error suspending user', error);
      throw error;
    }
  }

  // Properties Management
  async getAllProperties(params?: {
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{ data: any[]; total: number }> {
    try {
      const response: ApiResponse = await apiClient.get(
        ADMIN_ENDPOINTS.GET_ALL_PROPERTIES
      );
      return response.data; 
    } catch (error) {
      console.log('Error fetching all properties');
      throw error;
    }
  }

  async verifyProperty(propertyId: string): Promise<any> {
    try {
      const response = await apiClient.patch(
        ADMIN_ENDPOINTS.VERIFY_PROPERTY(propertyId)
      );
      return response.data;
    } catch (error) {
      console.log('Error verifying property', error);
      throw error;
    }
  }

  async unverifyProperty(propertyId: string): Promise<any> {
    try {
      const response = await apiClient.patch(
        ADMIN_ENDPOINTS.UNVERIFY_PROPERTY(propertyId)
      );
      return response.data; 
    } catch (error) {
      console.log('Error unverifying property', error);
      throw error;
    }
  }

  async featureProperty(propertyId: string): Promise<any> {
    try {
      const response = await apiClient.patch(
        ADMIN_ENDPOINTS.FEATURE_PROPERTY(propertyId)
      );
      return response.data; 
    } catch (error) {
      console.log('Error featuring property', error);
      throw error;
    }
  }

  async unfeatureProperty(propertyId: string): Promise<any> {
    try {
      const response = await apiClient.patch(
        ADMIN_ENDPOINTS.UNFEATURE_PROPERTY(propertyId)
      );
      return response.data; 
    } catch (error) {
      console.log('Error unfeaturing property', error);
      throw error;
    }
  }

  // Payments Management
  async getAllPayments(params?: {
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{ data: any[]; total: number }> {
    try {
      const response: ApiResponse = await apiClient.get(
        ADMIN_ENDPOINTS.GET_ALL_PAYMENTS, {
        params
      });
      return response.data;
    } catch (error) {
      console.log('Error fetching all payments');
      throw error;
    }
  }

  async approvePayment(paymentId: string): Promise<any> {
    try {
      const response = await apiClient.patch(
        ADMIN_ENDPOINTS.APPROVE_PAYMENT(paymentId)
      );
      return response.data; 
    } catch (error) {
      console.log('Error approving payment', error);
      throw error;
    }
  }

  async rejectPayment(
    paymentId: string, reason: string
  ): Promise<any> {
    try {
      const response = await apiClient.patch(
        ADMIN_ENDPOINTS.REJECT_PAYMENT(paymentId), {
        reason
      });
      return response.data; 
    } catch (error) {
      console.log('Error rejecting payment approval', error);
      throw error;
    }
  }

  // Bulk Actions
  async bulkAction(
    action: string, ids: string[], data?: any
  ): Promise<any> {
    const response = await apiClient.post(
      ADMIN_ENDPOINTS.BULK_ACTIONS, {
      action,
      ids,
      data
    });
    return response.data;
  }

  // Recent Activity
  async getRecentActivity(): Promise<any[]> {
    try {
      const response: ApiResponse = await apiClient.get(
        ADMIN_ENDPOINTS.GET_RECENT_ACTIVITIES
      );
      return response.data; 
    } catch (error) {
      
    }
  }

  // Pending Approvals
  async getPendingApprovals(): Promise<any> {
    try {
      const response = await apiClient.get(
        ADMIN_ENDPOINTS.GET_ITEMS_PENDING_APPROVAL
      );
      return response.data; 
    } catch (error) {
      console.log('Error fetching pending approvals');
    }
  }
}

export default new AdminService();
