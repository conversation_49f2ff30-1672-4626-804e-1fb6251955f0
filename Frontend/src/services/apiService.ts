// Real API Service to replace MockDataService
// This provides actual backend API integration

import { API_CONFIG } from '@/config/api';

// Types matching backend API responses
export interface ApiProperty {
  id: string;
  title: string;
  description: string;
  propertyType: string;
  status: string;
  pricePerYear: string;
  pricePerMonth: string;
  securityDeposit: string;
  location: string;
  city: string;
  state: string;
  country: string;
  bedrooms: number;
  bathrooms: number;
  toilets: number;
  sizeInSqm: string;
  furnishingStatus: string;
  amenities: string[];
  images: string[];
  videoUrl?: string;
  virtualTourUrl?: string;
  latitude: string;
  longitude: string;
  isFeatured: boolean;
  isVerified: boolean;
  viewsCount: number;
  inquiriesCount: number;
  landlordId: string;
  agentId?: string;
  createdAt: string;
  updatedAt: string;
  landlord?: ApiUser;
  agent?: ApiUser;
}

export interface ApiUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  role: string;
  status: string;
  isActive: boolean;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  avatar?: string;
  city: string;
  state: string;
  country: string;
  occupation?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ApiApplication {
  id: string;
  propertyId: string;
  tenantId: string;
  status: string;
  applicationDate: string;
  moveInDate?: string;
  monthlyIncome: string;
  employmentStatus: string;
  employerName?: string;
  employerPhone?: string;
  previousAddress?: string;
  reasonForMoving?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelationship?: string;
  additionalInfo?: string;
  createdAt: string;
  updatedAt: string;
  property?: ApiProperty;
  tenant?: ApiUser;
}

export interface ApiResponse<T> {
  statusCode: number;
  message: string;
  data: T;
  timestamp: string;
  path: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// API Service Class
export class ApiService {
  private static baseUrl = API_CONFIG.BASE_URL;
  private static timeout = API_CONFIG.TIMEOUT;

  // Generic fetch wrapper with error handling
  private static async fetchWithAuth<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const token = localStorage.getItem('accessToken');

    const config: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
    };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        ...config,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result: ApiResponse<T> = await response.json();
      return result.data;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      throw error;
    }
  }

  // =====================================================
  // PROPERTY OPERATIONS
  // =====================================================

  /**
   * Get all properties with optional filtering
   */
  static async getProperties(
    params: {
      page?: number;
      limit?: number;
      search?: string;
      propertyType?: string;
      minPrice?: number;
      maxPrice?: number;
      bedrooms?: number;
      bathrooms?: number;
      city?: string;
      state?: string;
      isVerified?: boolean;
      isFeatured?: boolean;
      sortBy?: string;
      sortOrder?: 'ASC' | 'DESC';
    } = {}
  ): Promise<PaginatedResponse<ApiProperty>> {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString());
      }
    });

    const queryString = searchParams.toString();
    const endpoint = `/properties/search${queryString ? `?${queryString}` : ''}`;

    return this.fetchWithAuth<PaginatedResponse<ApiProperty>>(endpoint);
  }

  /**
   * Get single property by ID
   */
  static async getProperty(id: string): Promise<ApiProperty> {
    return this.fetchWithAuth<ApiProperty>(`/properties/${id}`);
  }

  /**
   * Create new property
   */
  static async createProperty(propertyData: Partial<ApiProperty>): Promise<ApiProperty> {
    return this.fetchWithAuth<ApiProperty>('/properties/create', {
      method: 'POST',
      body: JSON.stringify(propertyData),
    });
  }

  /**
   * Update property
   */
  static async updateProperty(id: string, updates: Partial<ApiProperty>): Promise<ApiProperty> {
    return this.fetchWithAuth<ApiProperty>(`/properties/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    });
  }

  /**
   * Delete property
   */
  static async deleteProperty(id: string): Promise<void> {
    return this.fetchWithAuth<void>(`/properties/${id}`, {
      method: 'DELETE',
    });
  }

  /**
   * Record property inquiry
   */
  static async recordInquiry(propertyId: string): Promise<void> {
    return this.fetchWithAuth<void>(`/properties/${propertyId}/inquire`, {
      method: 'POST',
    });
  }

  // =====================================================
  // USER OPERATIONS
  // =====================================================

  /**
   * Get all users (admin only)
   */
  static async getUsers(
    params: {
      page?: number;
      limit?: number;
      role?: string;
      status?: string;
      search?: string;
    } = {}
  ): Promise<PaginatedResponse<ApiUser>> {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString());
      }
    });

    const queryString = searchParams.toString();
    const endpoint = `/users${queryString ? `?${queryString}` : ''}`;

    return this.fetchWithAuth<PaginatedResponse<ApiUser>>(endpoint);
  }

  /**
   * Get user by ID
   */
  static async getUser(id: string): Promise<ApiUser> {
    return this.fetchWithAuth<ApiUser>(`/users/${id}`);
  }

  /**
   * Update user
   */
  static async updateUser(id: string, updates: Partial<ApiUser>): Promise<ApiUser> {
    return this.fetchWithAuth<ApiUser>(`/users/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    });
  }

  // =====================================================
  // APPLICATION OPERATIONS
  // =====================================================

  /**
   * Get all applications
   */
  static async getApplications(
    params: {
      page?: number;
      limit?: number;
      status?: string;
      propertyId?: string;
      tenantId?: string;
    } = {}
  ): Promise<PaginatedResponse<ApiApplication>> {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString());
      }
    });

    const queryString = searchParams.toString();
    const endpoint = `/applications${queryString ? `?${queryString}` : ''}`;

    return this.fetchWithAuth<PaginatedResponse<ApiApplication>>(endpoint);
  }

  /**
   * Get application by ID
   */
  static async getApplication(id: string): Promise<ApiApplication> {
    return this.fetchWithAuth<ApiApplication>(`/applications/${id}`);
  }

  /**
   * Update application status
   */
  static async updateApplicationStatus(
    id: string,
    status: string,
    adminNotes?: string
  ): Promise<ApiApplication> {
    return this.fetchWithAuth<ApiApplication>(`/applications/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status, adminNotes }),
    });
  }

  // =====================================================
  // STATISTICS & ANALYTICS
  // =====================================================

  /**
   * Get dashboard statistics
   */
  static async getStats(): Promise<{
    totalProperties: number;
    totalUsers: number;
    totalApplications: number;
    pendingApplications: number;
    approvedApplications: number;
    rejectedApplications: number;
    totalRevenue: number;
    activeListings: number;
    verifiedProperties: number;
    featuredProperties: number;
  }> {
    return this.fetchWithAuth<any>('/analytics/dashboard-stats');
  }

  /**
   * Get property analytics
   */
  static async getPropertyAnalytics(propertyId: string): Promise<{
    views: number;
    inquiries: number;
    applications: number;
    viewsThisMonth: number;
    inquiriesThisMonth: number;
    conversionRate: number;
  }> {
    return this.fetchWithAuth<any>(`/analytics/properties/${propertyId}`);
  }

  // =====================================================
  // SEARCH OPERATIONS
  // =====================================================

  /**
   * Search properties with advanced filters
   */
  static async searchProperties(query: string, filters: any = {}): Promise<ApiProperty[]> {
    const params = {
      search: query,
      ...filters,
    };

    const result = await this.getProperties(params);
    return result.data;
  }

  // =====================================================
  // UTILITY OPERATIONS
  // =====================================================

  /**
   * Upload file
   */
  static async uploadFile(file: File, folder: string = 'general'): Promise<string> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('folder', folder);

    const token = localStorage.getItem('auth_token');

    const response = await fetch(`${this.baseUrl}/upload`, {
      method: 'POST',
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
      body: formData,
    });

    if (!response.ok) {
      throw new Error('File upload failed');
    }

    const result: ApiResponse<{ url: string }> = await response.json();
    return result.data.url;
  }

  // =====================================================
  // AGENT MANAGEMENT OPERATIONS
  // =====================================================

  /**
   * Get all agents with filtering and pagination
   */
  static async getAgents(
    filters: {
      page?: number;
      limit?: number;
      search?: string;
      status?: string;
      location?: string;
    } = {}
  ): Promise<PaginatedResponse<any>> {
    const params = new URLSearchParams();

    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.search) params.append('search', filters.search);
    if (filters.status) params.append('status', filters.status);
    if (filters.location) params.append('location', filters.location);

    const queryString = params.toString();
    const endpoint = `/agents${queryString ? `?${queryString}` : ''}`;

    return this.fetchWithAuth<PaginatedResponse<any>>(endpoint);
  }

  /**
   * Get agent statistics
   */
  static async getAgentStats(): Promise<{
    total: number;
    verified: number;
    pending: number;
    suspended: number;
    active_this_month: number;
  }> {
    return this.fetchWithAuth<any>('/agents/stats');
  }

  /**
   * Update agent status
   */
  static async updateAgentStatus(agentId: string, status: string): Promise<void> {
    return this.fetchWithAuth<void>(`/agents/${agentId}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status }),
    });
  }

  /**
   * Bulk update agents
   */
  static async bulkUpdateAgents(agentIds: string[], updates: any): Promise<void> {
    return this.fetchWithAuth<void>('/agents/bulk-update', {
      method: 'POST',
      body: JSON.stringify({ agent_ids: agentIds, updates }),
    });
  }

  /**
   * Get agent details
   */
  static async getAgentDetails(agentId: string): Promise<any> {
    return this.fetchWithAuth<any>(`/agents/${agentId}`);
  }

  /**
   * Get agent performance metrics
   */
  static async getAgentPerformance(agentId: string): Promise<any> {
    return this.fetchWithAuth<any>(`/agents/${agentId}/performance`);
  }

  // =====================================================
  // ANALYTICS OPERATIONS
  // =====================================================

  /**
   * Get application trends data
   */
  static async getApplicationTrends(): Promise<{
    monthlyData: Array<{
      month: string;
      applications: number;
      approved: number;
      rejected: number;
    }>;
  }> {
    return this.fetchWithAuth<any>('/analytics/applications/trends');
  }

  /**
   * Get average processing time
   */
  static async getAverageProcessingTime(): Promise<{
    averageDays: number;
    byStatus: Record<string, number>;
  }> {
    return this.fetchWithAuth<any>('/analytics/applications/processing-time');
  }

  /**
   * Get admin performance metrics
   */
  static async getAdminPerformance(
    adminId?: string,
    dateRange?: string
  ): Promise<{
    metrics: {
      applications_reviewed: number;
      average_review_time_hours: number;
      approval_rate: number;
    };
    dailyActivity: Array<{
      date: string;
      applications: number;
      approvals: number;
      avgTime: number;
    }>;
  }> {
    const params = new URLSearchParams();
    if (adminId) params.append('admin_id', adminId);
    if (dateRange) params.append('date_range', dateRange);

    const queryString = params.toString();
    const endpoint = `/analytics/admin/performance${queryString ? `?${queryString}` : ''}`;

    return this.fetchWithAuth<any>(endpoint);
  }

  /**
   * Health check
   */
  static async healthCheck(): Promise<{
    status: string;
    timestamp: string;
    uptime: number;
    version: string;
  }> {
    return this.fetchWithAuth<any>('/health');
  }
}

export default ApiService;
