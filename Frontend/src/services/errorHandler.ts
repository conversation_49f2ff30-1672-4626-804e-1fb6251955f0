// Centralized error handling service
interface ErrorContext {
  message: string;
  code: number;
  status: number;
  severity: 'low' | 'medium' | 'high';
  category: string;
  context?: Record<string, any>;
}

class ErrorHandler {
  private static instance: <PERSON>rror<PERSON>andler;

  public static getInstance(): <PERSON>rror<PERSON>andler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  handleError(error: ErrorContext): void {
    // Log error for monitoring/debugging
    console.error(`[${error.severity.toUpperCase()}] ${error.category}:`, {
      message: error.message,
      code: error.code,
      context: error.context,
    });

    // In a production app, you might want to send errors to a monitoring service
    // like Sentry, LogRocket, etc.
    if (error.severity === 'high') {
      this.notifyMonitoringService(error);
    }
  }

  private notifyMonitoringService(error: ErrorContext): void {
    // Placeholder for external monitoring service integration
    // Example: Sentry.captureException(error);
  }
}

export const errorHandler = ErrorHandler.getInstance();
