
import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useNotifications } from '@/hooks/useNotifications';
import { User } from 'lucide-react';
import DocumentViewer from './DocumentViewer';
import ApplicationPersonalInfo from './ApplicationPersonalInfo';  
import ApplicationOperatingAreas from './ApplicationOperatingAreas';
import ApplicationDocuments from './ApplicationDocuments';
import ApplicationRefereeInfo from './ApplicationRefereeInfo';
import ApplicationStatusUpdate from './ApplicationStatusUpdate';
import adminService, { Application } from '@/services/adminService';

type ApplicationStatus = 'pending_review' | 'approved' | 'rejected';

interface ApplicationDetailsModalProps {
  application: Application | null;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: () => void;
}

const ApplicationDetailsModal = ({ application, isOpen, onClose, onUpdate }: ApplicationDetailsModalProps) => {
  const [newStatus, setNewStatus] = useState<ApplicationStatus>('pending_review');
  const [reviewerNotes, setReviewerNotes] = useState('');
  const [nextAction, setNextAction] = useState('');
  const [updating, setUpdating] = useState(false);
  const [documents, setDocuments] = useState<any[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<any>(null);
  const { toast } = useToast();
  const { sendStatusUpdateNotification, sendRefereeContactNotification } = useNotifications();

  useEffect(() => {
    if (application) {
      setNewStatus(application.status);
      setReviewerNotes(application.reviewer_notes || '');
      setNextAction(application.next_action || '');
      fetchDocuments();
    }
  }, [application]);

  const fetchDocuments = async () => {
    if (!application) return;
    
    try {
      // For now, we'll use mock documents since the backend doesn't have this endpoint yet
      // TODO: Implement documents endpoint in backend
      const mockDocuments = [
        {
          id: '1',
          name: 'ID Card',
          type: 'image',
          url: 'https://via.placeholder.com/300x200?text=ID+Card',
          uploaded_at: new Date().toISOString()
        },
        {
          id: '2',
          name: 'Business Registration',
          type: 'pdf',
          url: 'https://via.placeholder.com/300x200?text=Business+Registration',
          uploaded_at: new Date().toISOString()
        }
      ];
      setDocuments(mockDocuments);
    } catch (error) {
      console.error('Error fetching documents:', error);
    }
  };

  const handleStatusUpdate = async () => {
    if (!application) return;

    const oldStatus = application.status;
    setUpdating(true);
    
    try {
      // Update application status using admin service
      await adminService.updateApplicationStatus(
        application.id,
        newStatus as 'approved' | 'rejected',
        reviewerNotes
      );

      // Send notifications for status changes
      if (newStatus !== oldStatus) {
        await sendStatusUpdateNotification(application.id, newStatus, oldStatus);
        
        // If status is referee_contacted, also notify referee
        if (newStatus === 'referee_contacted' && application.referee_verifications?.[0]) {
          await sendRefereeContactNotification(
            application.id, 
            application.referee_verifications[0].referee_whatsapp_number
          );
        }
      }

      toast({
        title: "Application Updated",
        description: "Application status has been updated successfully and notifications sent.",
      });

      onUpdate();
      onClose();
    } catch (error) {
      console.error('Error updating application:', error);
      toast({
        title: "Error",
        description: "Failed to update application status",
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  };

  if (!application) return null;

  const hasChanges = newStatus !== application.status || 
                    reviewerNotes !== (application.reviewer_notes || '') ||
                    nextAction !== (application.next_action || '');

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-3">
              <User className="w-5 h-5" />
              {application.full_name} - {application.agent_id}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            <ApplicationPersonalInfo application={application} />
            
            <ApplicationOperatingAreas operatingAreas={application.operating_areas} />
            
            <ApplicationDocuments 
              documents={documents}
              onViewDocument={setSelectedDocument}
            />

            <ApplicationRefereeInfo 
              refereeVerifications={application.referee_verifications || []}
            />

            <Separator />

            <ApplicationStatusUpdate 
              newStatus={newStatus}
              onStatusChange={setNewStatus}
              reviewerNotes={reviewerNotes}
              onReviewerNotesChange={setReviewerNotes}
              nextAction={nextAction}
              onNextActionChange={setNextAction}
              onUpdate={handleStatusUpdate}
              onCancel={onClose}
              updating={updating}
              hasChanges={hasChanges}
            />
          </div>
        </DialogContent>
      </Dialog>

      <DocumentViewer
        document={selectedDocument}
        isOpen={!!selectedDocument}
        onClose={() => setSelectedDocument(null)}
      />
    </>
  );
};

export default ApplicationDetailsModal;
