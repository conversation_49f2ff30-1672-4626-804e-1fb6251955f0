import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { 
  Eye, Search, Filter, CheckCircle, XCircle, AlertTriangle, 
  Clock, Users, Building2, FileText, Shield, Zap, MoreVertical,
  Flag, MessageSquare, Image, Video, File, Calendar
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface ContentItem {
  id: string;
  type: 'property' | 'review' | 'comment' | 'image' | 'document';
  title: string;
  description: string;
  content: string;
  status: 'pending' | 'approved' | 'rejected' | 'flagged';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  submittedBy: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  submittedAt: string;
  reviewedBy?: {
    id: string;
    name: string;
  };
  reviewedAt?: string;
  reviewNotes?: string;
  flags?: {
    reason: string;
    reportedBy: string;
    reportedAt: string;
  }[];
  metadata?: {
    propertyId?: string;
    propertyTitle?: string;
    images?: string[];
    category?: string;
  };
}

const AdminContentModeration = () => {
  const { toast } = useToast();
  const [contentItems, setContentItems] = useState<ContentItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('pending');
  const [typeFilter, setTypeFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [selectedItem, setSelectedItem] = useState<ContentItem | null>(null);
  const [reviewNotes, setReviewNotes] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Mock data for demonstration
  const mockContentItems: ContentItem[] = [
    {
      id: '1',
      type: 'property',
      title: 'Luxury 3-Bedroom Apartment',
      description: 'Beautiful apartment in GRA Phase 2',
      content: 'Spacious 3-bedroom apartment with modern amenities...',
      status: 'pending',
      priority: 'medium',
      submittedBy: {
        id: 'user1',
        name: 'Emeka Okafor',
        email: '<EMAIL>',
        role: 'agent'
      },
      submittedAt: '2024-01-15T10:30:00Z',
      metadata: {
        propertyId: 'prop1',
        propertyTitle: 'Luxury 3-Bedroom Apartment',
        images: ['/images/prop1-1.jpg', '/images/prop1-2.jpg'],
        category: 'residential'
      }
    },
    {
      id: '2',
      type: 'review',
      title: 'Property Review',
      description: 'Review for property ID: prop123',
      content: 'Great property, very clean and well-maintained...',
      status: 'flagged',
      priority: 'high',
      submittedBy: {
        id: 'user2',
        name: 'Blessing Eze',
        email: '<EMAIL>',
        role: 'tenant'
      },
      submittedAt: '2024-01-14T15:45:00Z',
      flags: [
        {
          reason: 'Inappropriate language',
          reportedBy: 'user3',
          reportedAt: '2024-01-15T09:20:00Z'
        }
      ]
    },
    {
      id: '3',
      type: 'image',
      title: 'Property Images',
      description: 'Images for property listing',
      content: 'Multiple images uploaded for property verification',
      status: 'pending',
      priority: 'low',
      submittedBy: {
        id: 'user4',
        name: 'Chinedu Okoro',
        email: '<EMAIL>',
        role: 'agent'
      },
      submittedAt: '2024-01-13T14:20:00Z',
      metadata: {
        images: ['/images/prop2-1.jpg', '/images/prop2-2.jpg', '/images/prop2-3.jpg']
      }
    }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setContentItems(mockContentItems);
      setLoading(false);
    }, 1000);
  }, []);

  const handleApproveContent = async (itemId: string) => {
    try {
      // This would call the actual API
      setContentItems(prev => prev.map(item => 
        item.id === itemId 
          ? { ...item, status: 'approved', reviewedAt: new Date().toISOString(), reviewNotes }
          : item
      ));
      toast({
        title: "Content Approved",
        description: "Content has been approved successfully",
      });
      setReviewNotes('');
      setSelectedItem(null);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to approve content",
        variant: "destructive",
      });
    }
  };

  const handleRejectContent = async (itemId: string, reason: string) => {
    try {
      // This would call the actual API
      setContentItems(prev => prev.map(item => 
        item.id === itemId 
          ? { ...item, status: 'rejected', reviewedAt: new Date().toISOString(), reviewNotes: reason }
          : item
      ));
      toast({
        title: "Content Rejected",
        description: "Content has been rejected",
      });
      setReviewNotes('');
      setSelectedItem(null);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reject content",
        variant: "destructive",
      });
    }
  };

  const handleFlagContent = async (itemId: string, reason: string) => {
    try {
      // This would call the actual API
      setContentItems(prev => prev.map(item => 
        item.id === itemId 
          ? { 
              ...item, 
              status: 'flagged',
              flags: [...(item.flags || []), {
                reason,
                reportedBy: 'admin',
                reportedAt: new Date().toISOString()
              }]
            }
          : item
      ));
      toast({
        title: "Content Flagged",
        description: "Content has been flagged for review",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to flag content",
        variant: "destructive",
      });
    }
  };

  const filteredItems = contentItems.filter(item => {
    if (statusFilter !== 'all' && item.status !== statusFilter) return false;
    if (typeFilter !== 'all' && item.type !== typeFilter) return false;
    if (priorityFilter !== 'all' && item.priority !== priorityFilter) return false;
    if (searchQuery && !item.title.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !item.description.toLowerCase().includes(searchQuery.toLowerCase())) return false;
    return true;
  });

  const stats = {
    total: contentItems.length,
    pending: contentItems.filter(item => item.status === 'pending').length,
    flagged: contentItems.filter(item => item.status === 'flagged').length,
    urgent: contentItems.filter(item => item.priority === 'urgent').length,
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'approved':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Approved</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />Rejected</Badge>;
      case 'flagged':
        return <Badge className="bg-orange-100 text-orange-800"><Flag className="w-3 h-3 mr-1" />Flagged</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <Badge className="bg-red-100 text-red-800">Urgent</Badge>;
      case 'high':
        return <Badge className="bg-orange-100 text-orange-800">High</Badge>;
      case 'medium':
        return <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>;
      case 'low':
        return <Badge className="bg-green-100 text-green-800">Low</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{priority}</Badge>;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'property':
        return <Building2 className="w-4 h-4" />;
      case 'review':
        return <MessageSquare className="w-4 h-4" />;
      case 'comment':
        return <MessageSquare className="w-4 h-4" />;
      case 'image':
        return <Image className="w-4 h-4" />;
      case 'document':
        return <File className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Content Moderation</h2>
          <p className="text-gray-600">Review and moderate user-generated content</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Shield className="w-4 h-4 mr-2" />
            Moderation Rules
          </Button>
          <Button className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700">
            <AlertTriangle className="w-4 h-4 mr-2" />
            Report Queue
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Items</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <FileText className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Review</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Flagged Content</p>
                <p className="text-2xl font-bold text-orange-600">{stats.flagged}</p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <Flag className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Urgent Items</p>
                <p className="text-2xl font-bold text-red-600">{stats.urgent}</p>
              </div>
              <div className="p-3 bg-red-100 rounded-full">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            <div className="flex items-center gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search content..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="flagged">Flagged</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Types</option>
                <option value="property">Properties</option>
                <option value="review">Reviews</option>
                <option value="comment">Comments</option>
                <option value="image">Images</option>
                <option value="document">Documents</option>
              </select>
              <select
                value={priorityFilter}
                onChange={(e) => setPriorityFilter(e.target.value)}
                className="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Priorities</option>
                <option value="urgent">Urgent</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Apply Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Content Items Table */}
      <Card>
        <CardHeader>
          <CardTitle>Content Queue</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-4 font-medium">Content</th>
                  <th className="text-left p-4 font-medium">Type</th>
                  <th className="text-left p-4 font-medium">Submitted By</th>
                  <th className="text-left p-4 font-medium">Priority</th>
                  <th className="text-left p-4 font-medium">Status</th>
                  <th className="text-left p-4 font-medium">Date</th>
                  <th className="text-left p-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredItems.map((item) => (
                  <tr key={item.id} className="border-b hover:bg-gray-50">
                    <td className="p-4">
                      <div>
                        <div className="font-medium text-gray-900">{item.title}</div>
                        <div className="text-sm text-gray-600 line-clamp-2">{item.description}</div>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center gap-2">
                        {getTypeIcon(item.type)}
                        <span className="capitalize">{item.type}</span>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="text-sm">
                        <div className="font-medium">{item.submittedBy.name}</div>
                        <div className="text-gray-600">{item.submittedBy.role}</div>
                      </div>
                    </td>
                    <td className="p-4">
                      {getPriorityBadge(item.priority)}
                    </td>
                    <td className="p-4">
                      {getStatusBadge(item.status)}
                    </td>
                    <td className="p-4">
                      <div className="text-sm">
                        <div>{new Date(item.submittedAt).toLocaleDateString()}</div>
                        <div className="text-gray-600">{new Date(item.submittedAt).toLocaleTimeString()}</div>
                      </div>
                    </td>
                    <td className="p-4">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <Dialog>
                            <DialogTrigger asChild>
                              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                <Eye className="w-4 h-4 mr-2" />
                                Review
                              </DropdownMenuItem>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl">
                              <DialogHeader>
                                <DialogTitle>Review Content</DialogTitle>
                                <DialogDescription>
                                  Review the content and take appropriate action
                                </DialogDescription>
                              </DialogHeader>
                              <div className="space-y-4">
                                <div>
                                  <h4 className="font-medium">{item.title}</h4>
                                  <p className="text-sm text-gray-600">{item.description}</p>
                                </div>
                                <div>
                                  <h5 className="font-medium mb-2">Content:</h5>
                                  <div className="bg-gray-50 p-3 rounded-lg">
                                    <p className="text-sm">{item.content}</p>
                                  </div>
                                </div>
                                {item.flags && item.flags.length > 0 && (
                                  <div>
                                    <h5 className="font-medium mb-2 text-red-600">Flags:</h5>
                                    {item.flags.map((flag, index) => (
                                      <div key={index} className="bg-red-50 p-3 rounded-lg mb-2">
                                        <p className="text-sm font-medium">{flag.reason}</p>
                                        <p className="text-xs text-gray-600">
                                          Reported by {flag.reportedBy} on {new Date(flag.reportedAt).toLocaleDateString()}
                                        </p>
                                      </div>
                                    ))}
                                  </div>
                                )}
                                <div>
                                  <label className="text-sm font-medium">Review Notes:</label>
                                  <Textarea
                                    value={reviewNotes}
                                    onChange={(e) => setReviewNotes(e.target.value)}
                                    placeholder="Add review notes..."
                                    className="mt-1"
                                  />
                                </div>
                              </div>
                              <DialogFooter>
                                <Button
                                  variant="outline"
                                  onClick={() => {
                                    setSelectedItem(item);
                                    handleFlagContent(item.id, reviewNotes || 'Flagged by admin');
                                  }}
                                >
                                  <Flag className="w-4 h-4 mr-2" />
                                  Flag
                                </Button>
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button variant="destructive">
                                      <XCircle className="w-4 h-4 mr-2" />
                                      Reject
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>Reject Content</AlertDialogTitle>
                                      <AlertDialogDescription>
                                        Are you sure you want to reject this content? This action cannot be undone.
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() => handleRejectContent(item.id, reviewNotes || 'Rejected by admin')}
                                        className="bg-red-600 hover:bg-red-700"
                                      >
                                        Reject
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                                <Button
                                  onClick={() => handleApproveContent(item.id)}
                                  className="bg-green-600 hover:bg-green-700"
                                >
                                  <CheckCircle className="w-4 h-4 mr-2" />
                                  Approve
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                          <DropdownMenuItem>
                            <Flag className="w-4 h-4 mr-2" />
                            Flag
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Users className="w-4 h-4 mr-2" />
                            View User
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminContentModeration;
