import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { 
  Zap, Users, Building2, CreditCard, FileText, CheckCircle, 
  XCircle, AlertTriangle, Clock, Filter, Search, Download,
  Upload, Trash2, Shield, Star, Eye, MoreVertical, Settings
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface BulkOperation {
  id: string;
  type: 'users' | 'properties' | 'payments' | 'applications';
  action: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  itemsCount: number;
  createdAt: string;
  completedAt?: string;
  notes?: string;
  results?: {
    successful: number;
    failed: number;
    errors: string[];
  };
}

interface BulkItem {
  id: string;
  type: string;
  name: string;
  status: string;
  selected: boolean;
}

const AdminBulkOperations = () => {
  const { toast } = useToast();
  const [selectedOperation, setSelectedOperation] = useState<string>('');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [operationNotes, setOperationNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [bulkOperations, setBulkOperations] = useState<BulkOperation[]>([]);
  const [items, setItems] = useState<BulkItem[]>([]);
  const [filterType, setFilterType] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Mock data for demonstration
  const mockBulkOperations: BulkOperation[] = [
    {
      id: '1',
      type: 'users',
      action: 'activate',
      status: 'completed',
      itemsCount: 25,
      createdAt: '2024-01-15T10:30:00Z',
      completedAt: '2024-01-15T10:32:00Z',
      notes: 'Bulk activation of verified users',
      results: {
        successful: 23,
        failed: 2,
        errors: ['User ID 123 not found', 'User ID 456 already active']
      }
    },
    {
      id: '2',
      type: 'properties',
      action: 'verify',
      status: 'processing',
      itemsCount: 15,
      createdAt: '2024-01-15T09:15:00Z',
      notes: 'Bulk verification of properties'
    },
    {
      id: '3',
      type: 'payments',
      action: 'approve',
      status: 'pending',
      itemsCount: 8,
      createdAt: '2024-01-15T08:45:00Z',
      notes: 'Bulk approval of pending payments'
    }
  ];

  const mockItems: BulkItem[] = [
    { id: '1', type: 'user', name: 'Emeka Okafor', status: 'active', selected: false },
    { id: '2', type: 'user', name: 'Blessing Eze', status: 'inactive', selected: false },
    { id: '3', type: 'property', name: 'Luxury 3-Bedroom Apartment', status: 'pending', selected: false },
    { id: '4', type: 'property', name: 'Modern 2-Bedroom Flat', status: 'verified', selected: false },
    { id: '5', type: 'payment', name: 'Payment #PAY001', status: 'pending', selected: false },
    { id: '6', type: 'application', name: 'Agent Application #APP001', status: 'reviewed', selected: false },
  ];

  useEffect(() => {
    setBulkOperations(mockBulkOperations);
    setItems(mockItems);
  }, []);

  const handleSelectAll = () => {
    const allSelected = items.every(item => selectedItems.includes(item.id));
    if (allSelected) {
      setSelectedItems([]);
    } else {
      setSelectedItems(items.map(item => item.id));
    }
  };

  const handleSelectItem = (itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleBulkOperation = async () => {
    if (!selectedOperation || selectedItems.length === 0) {
      toast({
        title: "Error",
        description: "Please select an operation and items",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const newOperation: BulkOperation = {
        id: Date.now().toString(),
        type: selectedOperation as any,
        action: 'bulk_action',
        status: 'completed',
        itemsCount: selectedItems.length,
        createdAt: new Date().toISOString(),
        completedAt: new Date().toISOString(),
        notes: operationNotes,
        results: {
          successful: selectedItems.length - 1,
          failed: 1,
          errors: ['One item failed to process']
        }
      };

      setBulkOperations(prev => [newOperation, ...prev]);
      setSelectedItems([]);
      setOperationNotes('');
      setSelectedOperation('');

      toast({
        title: "Bulk Operation Completed",
        description: `Successfully processed ${selectedItems.length} items`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to perform bulk operation",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredItems = items.filter(item => {
    if (filterType !== 'all' && item.type !== filterType) return false;
    if (searchQuery && !item.name.toLowerCase().includes(searchQuery.toLowerCase())) return false;
    return true;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Completed</Badge>;
      case 'processing':
        return <Badge className="bg-blue-100 text-blue-800"><Clock className="w-3 h-3 mr-1" />Processing</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />Failed</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const getOperationIcon = (type: string) => {
    switch (type) {
      case 'users':
        return <Users className="w-4 h-4" />;
      case 'properties':
        return <Building2 className="w-4 h-4" />;
      case 'payments':
        return <CreditCard className="w-4 h-4" />;
      case 'applications':
        return <FileText className="w-4 h-4" />;
      default:
        return <Settings className="w-4 h-4" />;
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Bulk Operations</h2>
          <p className="text-gray-600">Perform actions on multiple items simultaneously</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline">
            <Upload className="w-4 h-4 mr-2" />
            Import
          </Button>
        </div>
      </div>

      {/* Bulk Operation Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            New Bulk Operation
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium">Operation Type</label>
              <Select value={selectedOperation} onValueChange={setSelectedOperation}>
                <SelectTrigger>
                  <SelectValue placeholder="Select operation type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="users">Users</SelectItem>
                  <SelectItem value="properties">Properties</SelectItem>
                  <SelectItem value="payments">Payments</SelectItem>
                  <SelectItem value="applications">Applications</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Action</label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select action" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="activate">Activate</SelectItem>
                  <SelectItem value="deactivate">Deactivate</SelectItem>
                  <SelectItem value="verify">Verify</SelectItem>
                  <SelectItem value="approve">Approve</SelectItem>
                  <SelectItem value="reject">Reject</SelectItem>
                  <SelectItem value="delete">Delete</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Selected Items</label>
              <div className="p-2 bg-gray-50 rounded border">
                <span className="text-sm font-medium">{selectedItems.length} items selected</span>
              </div>
            </div>
          </div>
          <div>
            <label className="text-sm font-medium">Operation Notes</label>
            <Textarea
              value={operationNotes}
              onChange={(e) => setOperationNotes(e.target.value)}
              placeholder="Add notes about this bulk operation..."
              className="mt-1"
            />
          </div>
          <div className="flex gap-2">
            <Button 
              onClick={handleBulkOperation} 
              disabled={loading || selectedItems.length === 0}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              <Zap className="w-4 h-4 mr-2" />
              {loading ? 'Processing...' : 'Execute Bulk Operation'}
            </Button>
            <Button variant="outline" onClick={() => setSelectedItems([])}>
              Clear Selection
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Items Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Items for Bulk Operation</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between mb-4">
            <div className="flex items-center gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search items..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Types</option>
                <option value="user">Users</option>
                <option value="property">Properties</option>
                <option value="payment">Payments</option>
                <option value="application">Applications</option>
              </select>
            </div>
            <Button variant="outline" onClick={handleSelectAll}>
              {selectedItems.length === items.length ? 'Deselect All' : 'Select All'}
            </Button>
          </div>

          {/* Items Table */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-4 font-medium">
                    <input
                      type="checkbox"
                      checked={selectedItems.length === items.length && items.length > 0}
                      onChange={handleSelectAll}
                      className="rounded"
                    />
                  </th>
                  <th className="text-left p-4 font-medium">Type</th>
                  <th className="text-left p-4 font-medium">Name</th>
                  <th className="text-left p-4 font-medium">Status</th>
                  <th className="text-left p-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredItems.map((item) => (
                  <tr key={item.id} className="border-b hover:bg-gray-50">
                    <td className="p-4">
                      <input
                        type="checkbox"
                        checked={selectedItems.includes(item.id)}
                        onChange={() => handleSelectItem(item.id)}
                        className="rounded"
                      />
                    </td>
                    <td className="p-4">
                      <div className="flex items-center gap-2">
                        {getOperationIcon(item.type)}
                        <span className="capitalize">{item.type}</span>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="font-medium">{item.name}</div>
                    </td>
                    <td className="p-4">
                      {getStatusBadge(item.status)}
                    </td>
                    <td className="p-4">
                      <Button variant="ghost" size="sm">
                        <Eye className="w-4 h-4" />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Recent Bulk Operations */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Bulk Operations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {bulkOperations.map((operation) => (
              <div key={operation.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    {getOperationIcon(operation.type)}
                    <div>
                      <h4 className="font-medium capitalize">
                        {operation.action} {operation.type}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {operation.itemsCount} items • {new Date(operation.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  {getStatusBadge(operation.status)}
                </div>
                {operation.notes && (
                  <p className="text-sm text-gray-600 mb-2">{operation.notes}</p>
                )}
                {operation.results && (
                  <div className="flex items-center gap-4 text-sm">
                    <span className="text-green-600">✓ {operation.results.successful} successful</span>
                    <span className="text-red-600">✗ {operation.results.failed} failed</span>
                    {operation.results.errors.length > 0 && (
                      <span className="text-orange-600">⚠ {operation.results.errors.length} errors</span>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminBulkOperations;
