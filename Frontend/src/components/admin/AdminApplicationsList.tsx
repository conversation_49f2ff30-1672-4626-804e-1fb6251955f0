
import React, { useState, useEffect } from 'react';
import ApplicationsToolbar from '@/components/admin/applications/ApplicationsToolbar';
import ApplicationsGrid from '@/components/admin/applications/ApplicationsGrid';
import ApplicationDetailsModal from '@/components/admin/ApplicationDetailsModal';
import ApplicationsLoadingSkeleton from '@/components/admin/applications/ApplicationsLoadingSkeleton';
import { usePagination } from '@/hooks/usePagination';
import ErrorState from '@/components/common/ErrorState';
import adminService, { Application } from '@/services/adminService';

type ApplicationStatus = 'pending_review' | 'approved' | 'rejected';

const AdminApplicationsList = () => {
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const [filters, setFilters] = useState({
    status: 'all',
    operatingArea: 'all',
    dateRange: null,
    searchTerm: '',
  });
  
  const [selectedApplications, setSelectedApplications] = useState<string[]>([]);
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const fetchApplications = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params: any = {};
      if (filters.status !== 'all') params.status = filters.status;
      if (filters.searchTerm) params.search = filters.searchTerm;
      
      const response = await adminService.getApplications(params);
      setApplications(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch applications');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchApplications();
  }, [filters]);

  // Filter applications based on current filters
  const filteredApplications = applications.filter((app) => {
    if (filters.status !== 'all' && app.status !== filters.status) return false;
    if (filters.operatingArea !== 'all' && !app.operating_areas.includes(filters.operatingArea)) return false;
    if (filters.searchTerm && !app.full_name.toLowerCase().includes(filters.searchTerm.toLowerCase()) && 
        !app.email.toLowerCase().includes(filters.searchTerm.toLowerCase()) &&
        !app.agent_id.toLowerCase().includes(filters.searchTerm.toLowerCase())) return false;
    return true;
  });

  const pagination = usePagination({ data: filteredApplications, itemsPerPage: 10 });

  const handleApplicationSelect = (applicationId: string) => {
    setSelectedApplications(prev => 
      prev.includes(applicationId)
        ? prev.filter(id => id !== applicationId)
        : [...prev, applicationId]
    );
  };

  const handleSelectAll = (selectAll: boolean) => {
    setSelectedApplications(selectAll ? pagination.paginatedData.map((app: Application) => app.id) : []);
  };

  const handleApplicationClick = (application: Application) => {
    setSelectedApplication(application);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedApplication(null);
  };

  const handleApplicationUpdate = async () => {
    // Refetch data to get the latest updates
    await fetchApplications();
    setIsModalOpen(false);
    setSelectedApplication(null);
  };

  const handleBulkAction = (action: string) => {
    console.log(`Performing bulk action: ${action} on applications:`, selectedApplications);
    // Implement bulk actions here
  };

  const handleClearSelection = () => {
    setSelectedApplications([]);
  };

  if (error) {
    return (
      <ErrorState
        title="Failed to Load Applications"
        message={error}
        onRetry={fetchApplications}
        showRetry={true}
        showHome={false}
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Agent Applications</h2>
          <p className="text-gray-600 mt-1">
            Manage and review agent verification applications
          </p>
        </div>
        
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-md">
            {filteredApplications.length} Total
          </span>
          <span className="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-md">
            {filteredApplications.filter(app => app.status === 'pending_review').length} Pending
          </span>
          <span className="px-2 py-1 bg-green-100 text-green-700 rounded-md">
            {filteredApplications.filter(app => app.status === 'approved').length} Approved
          </span>
        </div>
      </div>

      <ApplicationsToolbar
        selectedCount={selectedApplications.length}
        totalCount={filteredApplications.length}
        onClearSelection={handleClearSelection}
        filters={filters}
        onFiltersChange={setFilters}
        onBulkAction={handleBulkAction}
      />

      {loading ? (
        <ApplicationsLoadingSkeleton count={5} />
      ) : (
        <ApplicationsGrid
          applications={pagination.paginatedData as Application[]}
          selectedApplications={selectedApplications}
          onSelectApplication={handleApplicationSelect}
          onSelectAll={handleSelectAll}
          onViewDetails={handleApplicationClick}
          currentPage={pagination.currentPage}
          totalPages={pagination.totalPages}
          onPageChange={pagination.goToPage}
          hasNextPage={pagination.hasNextPage}
          hasPreviousPage={pagination.hasPreviousPage}
          startIndex={pagination.startIndex}
          endIndex={pagination.endIndex}
          totalItems={pagination.totalItems}
          paginatedApplications={pagination.paginatedData as Application[]}
        />
      )}

      {selectedApplication && (
        <ApplicationDetailsModal
          application={selectedApplication}
          isOpen={isModalOpen}
          onClose={handleModalClose}
          onUpdate={handleApplicationUpdate}
        />
      )}
    </div>
  );
};

export default AdminApplicationsList;
