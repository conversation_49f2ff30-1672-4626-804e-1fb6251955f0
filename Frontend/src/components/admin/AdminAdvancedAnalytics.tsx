import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { 
  Bar<PERSON>hart3, TrendingUp, Users, Building2, CreditCard, 
  Download, RefreshCw, Eye, Activity,
  DollarSign, MapPin, Star, Clock, AlertTriangle, Zap
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface AnalyticsData {
  overview: {
    totalUsers: number;
    totalProperties: number;
    totalRevenue: number;
    activeAgents: number;
    conversionRate: number;
    avgResponseTime: number;
  };
  trends: {
    userGrowth: number;
    propertyGrowth: number;
    revenueGrowth: number;
    agentGrowth: number;
  };
  topPerformers: {
    agents: Array<{
      id: string;
      name: string;
      properties: number;
      sales: number;
      rating: number;
    }>;
    properties: Array<{
      id: string;
      title: string;
      views: number;
      inquiries: number;
      price: number;
    }>;
  };
  revenue: {
    monthly: Array<{
      month: string;
      amount: number;
      transactions: number;
    }>;
    byType: Array<{
      type: string;
      amount: number;
      percentage: number;
    }>;
  };
}

const AdminAdvancedAnalytics = () => {
  const { toast } = useToast();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');
  const [selectedMetric, setSelectedMetric] = useState('overview');

  // Mock data for demonstration
  const mockAnalyticsData: AnalyticsData = {
    overview: {
      totalUsers: 1247,
      totalProperties: 847,
      totalRevenue: 45600000,
      activeAgents: 89,
      conversionRate: 23.4,
      avgResponseTime: 0.6,
    },
    trends: {
      userGrowth: 15.2,
      propertyGrowth: 8.7,
      revenueGrowth: 23.1,
      agentGrowth: 12.4,
    },
    topPerformers: {
      agents: [
        { id: '1', name: 'Emeka Okafor', properties: 23, sales: 2847000, rating: 4.8 },
        { id: '2', name: 'Blessing Eze', properties: 31, sales: 1923000, rating: 4.9 },
        { id: '3', name: 'Chinedu Okoro', properties: 18, sales: 1456000, rating: 4.7 },
      ],
      properties: [
        { id: '1', title: 'Luxury 3-Bedroom Apartment', views: 1247, inquiries: 89, price: 4500000 },
        { id: '2', title: 'Modern 2-Bedroom Flat', views: 987, inquiries: 67, price: 3200000 },
        { id: '3', title: 'Executive Office Space', views: 756, inquiries: 45, price: 2800000 },
      ],
    },
    revenue: {
      monthly: [
        { month: 'Jan', amount: 3200000, transactions: 45 },
        { month: 'Feb', amount: 3800000, transactions: 52 },
        { month: 'Mar', amount: 4200000, transactions: 58 },
        { month: 'Apr', amount: 3900000, transactions: 54 },
        { month: 'May', amount: 4500000, transactions: 62 },
        { month: 'Jun', amount: 4800000, transactions: 67 },
      ],
      byType: [
        { type: 'Rent Payments', amount: 32000000, percentage: 70 },
        { type: 'Agent Commissions', amount: 9600000, percentage: 21 },
        { type: 'Service Fees', amount: 4000000, percentage: 9 },
      ],
    },
  };

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setAnalyticsData(mockAnalyticsData);
      setLoading(false);
    }, 1000);
  }, [timeRange]);

  const handleExportData = () => {
    toast({
      title: "Export Started",
      description: "Analytics data is being prepared for download",
    });
  };

  const handleRefreshData = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      toast({
        title: "Data Refreshed",
        description: "Analytics data has been updated",
      });
    }, 1000);
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return <div>No data available</div>;
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Advanced Analytics</h2>
          <p className="text-gray-600">Comprehensive insights and performance metrics</p>
        </div>
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleRefreshData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={handleExportData}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.overview.totalUsers.toLocaleString()}</p>
                <p className="text-sm text-green-600 flex items-center gap-1">
                  <TrendingUp className="w-3 h-3" />
                  +{analyticsData.trends.userGrowth}%
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Properties</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.overview.totalProperties.toLocaleString()}</p>
                <p className="text-sm text-green-600 flex items-center gap-1">
                  <TrendingUp className="w-3 h-3" />
                  +{analyticsData.trends.propertyGrowth}%
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Building2 className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">₦{(analyticsData.overview.totalRevenue / 1000000).toFixed(1)}M</p>
                <p className="text-sm text-green-600 flex items-center gap-1">
                  <TrendingUp className="w-3 h-3" />
                  +{analyticsData.trends.revenueGrowth}%
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <DollarSign className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Agents</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.overview.activeAgents}</p>
                <p className="text-sm text-green-600 flex items-center gap-1">
                  <TrendingUp className="w-3 h-3" />
                  +{analyticsData.trends.agentGrowth}%
                </p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <Star className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              Performance Metrics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-full">
                  <TrendingUp className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <p className="font-medium">Conversion Rate</p>
                  <p className="text-sm text-gray-600">Property inquiries to sales</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-xl font-bold text-green-600">{analyticsData.overview.conversionRate}%</p>
              </div>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-full">
                  <Clock className="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium">Avg Response Time</p>
                  <p className="text-sm text-gray-600">Agent response to inquiries</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-xl font-bold text-blue-600">{analyticsData.overview.avgResponseTime}h</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Revenue Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.revenue.byType.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${
                      index === 0 ? 'bg-blue-500' : 
                      index === 1 ? 'bg-green-500' : 'bg-orange-500'
                    }`}></div>
                    <span className="font-medium">{item.type}</span>
                  </div>
                  <div className="text-right">
                    <p className="font-bold">₦{(item.amount / 1000000).toFixed(1)}M</p>
                    <p className="text-sm text-gray-600">{item.percentage}%</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Performers */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="w-5 h-5" />
              Top Performing Agents
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.topPerformers.agents.map((agent, index) => (
                <div key={agent.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                    </div>
                    <div>
                      <p className="font-medium">{agent.name}</p>
                      <p className="text-sm text-gray-600">{agent.properties} properties</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold">₦{(agent.sales / 1000000).toFixed(1)}M</p>
                    <div className="flex items-center gap-1">
                      <Star className="w-3 h-3 text-yellow-400 fill-current" />
                      <span className="text-sm">{agent.rating}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="w-5 h-5" />
              Most Viewed Properties
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.topPerformers.properties.map((property, index) => (
                <div key={property.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-green-600">{index + 1}</span>
                    </div>
                    <div>
                      <p className="font-medium line-clamp-1">{property.title}</p>
                      <p className="text-sm text-gray-600">{property.views} views</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold">₦{(property.price / 1000000).toFixed(1)}M</p>
                    <p className="text-sm text-gray-600">{property.inquiries} inquiries</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Revenue Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Monthly Revenue Trend
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-end justify-between gap-2">
            {analyticsData.revenue.monthly.map((month, index) => {
              const maxAmount = Math.max(...analyticsData.revenue.monthly.map(m => m.amount));
              const height = (month.amount / maxAmount) * 100;
              return (
                <div key={month.month} className="flex-1 flex flex-col items-center">
                  <div className="w-full bg-blue-100 rounded-t-lg relative" style={{ height: `${height}%` }}>
                    <div className="absolute inset-0 bg-blue-500 rounded-t-lg opacity-80"></div>
                  </div>
                  <div className="text-center mt-2">
                    <p className="text-sm font-medium">{month.month}</p>
                    <p className="text-xs text-gray-600">₦{(month.amount / 1000000).toFixed(1)}M</p>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminAdvancedAnalytics;
