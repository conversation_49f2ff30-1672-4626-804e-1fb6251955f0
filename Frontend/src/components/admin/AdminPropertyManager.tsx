import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import AdminService from '@/services/adminService';
import { 
  Building2, Search, Filter, Eye, Edit, Trash2, CheckCircle, 
  XCircle, Star, MapPin, DollarSign, Users, Calendar,
  Plus, MoreVertical, AlertTriangle, Shield, Zap
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { propertyService } from '@/services/properties';

interface Property {
  id: string;
  title: string;
  description: string;
  location: string;
  pricePerYear: number;
  pricePerMonth: number;
  bedrooms: number;
  bathrooms: number;
  sizeInSqm: number;
  propertyType: string;
  amenities: string[];
  images: string[];
  isAvailable: boolean;
  isVerified: boolean;
  featured: boolean;
  agentId?: string;
  landlordId?: string;
  createdAt: string;
  updatedAt: string;
  agent?: {
    fullName: string;
    email: string;
  };
  landlord?: {
    fullName: string;
    email: string;
  };
}

const AdminPropertyManager = () => {
  const { toast } = useToast();
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchProperties = async () => {
    try {
      setLoading(true);
      const response = await AdminService.getAllProperties({
        search: searchQuery,
        page: currentPage,
        limit: 20
      });
      setProperties(response.data);
      setTotalPages(Math.ceil(response.total / 20));
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch properties",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProperties();
  }, [searchQuery, currentPage]);

  const handleVerifyProperty = async (propertyId: string) => {
    try {
      await AdminService.verifyProperty(propertyId);
      toast({
        title: "Property Verified",
        description: "Property has been marked as verified",
      });
      fetchProperties();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to verify property",
        variant: "destructive",
      });
    }
  };

  const handleUnverifyProperty = async (propertyId: string) => {
    try {
      await AdminService.unverifyProperty(propertyId);
      toast({
        title: "Property Unverified",
        description: "Property verification has been removed",
      });
      fetchProperties();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to unverify property",
        variant: "destructive",
      });
    }
  };

  const handleFeatureProperty = async (propertyId: string) => {
    try {
      await AdminService.featureProperty(propertyId);
      toast({
        title: "Property Featured",
        description: "Property has been featured",
      });
      fetchProperties();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to feature property",
        variant: "destructive",
      });
    }
  };

  const handleUnfeatureProperty = async (propertyId: string) => {
    try {
      await AdminService.unfeatureProperty(propertyId);
      toast({
        title: "Property Unfeatured",
        description: "Property has been unfeatured",
      });
      fetchProperties();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to unfeature property",
        variant: "destructive",
      });
    }
  };

  const handleDeleteProperty = async (propertyId: string) => {
    try {
      await propertyService.deleteProperty;
      toast({
        title: "Property Deleted",
        description: "Property has been permanently deleted",
      });
      fetchProperties();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete property",
        variant: "destructive",
      });
    }
  };

  const filteredProperties = properties.filter(property => {
    if (statusFilter === 'verified' && !property.isVerified) return false;
    if (statusFilter === 'unverified' && property.isVerified) return false;
    if (statusFilter === 'featured' && !property.featured) return false;
    if (statusFilter === 'available' && !property.isAvailable) return false;
    if (typeFilter !== 'all' && property.propertyType !== typeFilter) return false;
    return true;
  });

  const stats = {
    total: properties.length,
    verified: properties.filter(p => p.isVerified).length,
    featured: properties.filter(p => p.featured).length,
    available: properties.filter(p => p.isAvailable).length,
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Property Management</h2>
          <p className="text-gray-600">Manage all properties in the system</p>
        </div>
        <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
          <Plus className="w-4 h-4 mr-2" />
          Add Property
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Properties</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Building2 className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Verified</p>
                <p className="text-2xl font-bold text-green-600">{stats.verified}</p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Featured</p>
                <p className="text-2xl font-bold text-orange-600">{stats.featured}</p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <Star className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Available</p>
                <p className="text-2xl font-bold text-purple-600">{stats.available}</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Zap className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            <div className="flex items-center gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search properties..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Status</option>
                <option value="verified">Verified</option>
                <option value="unverified">Unverified</option>
                <option value="featured">Featured</option>
                <option value="available">Available</option>
              </select>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Types</option>
                <option value="apartment">Apartment</option>
                <option value="house">House</option>
                <option value="commercial">Commercial</option>
                <option value="land">Land</option>
              </select>
            </div>
            <Button variant="outline" onClick={fetchProperties}>
              <Filter className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Properties Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Properties</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-4 font-medium">Property</th>
                  <th className="text-left p-4 font-medium">Location</th>
                  <th className="text-left p-4 font-medium">Price</th>
                  <th className="text-left p-4 font-medium">Agent/Landlord</th>
                  <th className="text-left p-4 font-medium">Status</th>
                  <th className="text-left p-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredProperties.map((property) => (
                  <tr key={property.id} className="border-b hover:bg-gray-50">
                    <td className="p-4">
                      <div>
                        <div className="font-medium text-gray-900">{property.title}</div>
                        <div className="text-sm text-gray-600">
                          {property.bedrooms} bed • {property.bathrooms} bath • {property.sizeInSqm} sqft
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-gray-400" />
                        <span className="text-sm">{property.location}</span>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center gap-2">
                        <DollarSign className="w-4 h-4 text-green-600" />
                        <span className="font-medium">₦{property.pricePerYear.toLocaleString()}/year</span>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="text-sm">
                        <div className="font-medium">
                          {property.agent?.fullName || property.landlord?.fullName}
                        </div>
                        <div className="text-gray-600">
                          {property.agent?.email || property.landlord?.email}
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex flex-wrap gap-2">
                        {property.isVerified && (
                          <Badge className="bg-green-100 text-green-800">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Verified
                          </Badge>
                        )}
                        {property.featured && (
                          <Badge className="bg-orange-100 text-orange-800">
                            <Star className="w-3 h-3 mr-1" />
                            Featured
                          </Badge>
                        )}
                        {property.isAvailable ? (
                          <Badge className="bg-blue-100 text-blue-800">Available</Badge>
                        ) : (
                          <Badge className="bg-gray-100 text-gray-800">Occupied</Badge>
                        )}
                      </div>
                    </td>
                    <td className="p-4">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => window.open(`/properties/${property.id}`, '_blank')}>
                            <Eye className="w-4 h-4 mr-2" />
                            View
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="w-4 h-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          {property.isVerified ? (
                            <DropdownMenuItem onClick={() => handleUnverifyProperty(property.id)}>
                              <XCircle className="w-4 h-4 mr-2" />
                              Unverify
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem onClick={() => handleVerifyProperty(property.id)}>
                              <CheckCircle className="w-4 h-4 mr-2" />
                              Verify
                            </DropdownMenuItem>
                          )}
                          {property.featured ? (
                            <DropdownMenuItem onClick={() => handleUnfeatureProperty(property.id)}>
                              <XCircle className="w-4 h-4 mr-2" />
                              Unfeature
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem onClick={() => handleFeatureProperty(property.id)}>
                              <Star className="w-4 h-4 mr-2" />
                              Feature
                            </DropdownMenuItem>
                          )}
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                <Trash2 className="w-4 h-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Property</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete "{property.title}"? This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDeleteProperty(property.id)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminPropertyManager;
