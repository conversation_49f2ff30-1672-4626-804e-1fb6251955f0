
import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Button } from '@/components/ui/button';
import { Upload, X, Image as ImageIcon, Plus, GripVertical } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { PropertyImage, ImageUploadRequest, ReorderImagesRequest } from '@/types/property';
import { propertyImagesService } from '@/services/properties/propertyImagesService';

interface PropertyImageUploadProps {
  propertyId?: string;
  images: PropertyImage[];
  onImagesChange: (images: PropertyImage[]) => void;
  maxImages?: number;
  className?: string;
}

export interface PropertyImageUploadRef {
  uploadTempImages: () => Promise<void>;
}

// Client-side image compression utility
const compressImage = (file: File, maxWidth: number = 1920, quality: number = 0.8): Promise<File> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    const img = new Image();
    
    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      canvas.width = width;
      canvas.height = height;
      
      // Draw and compress
      ctx.drawImage(img, 0, 0, width, height);
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          } else {
            resolve(file);
          }
        },
        file.type,
        quality
      );
    };
    
    img.src = URL.createObjectURL(file);
  });
};

// Temporary image interface for files that haven't been uploaded yet
interface TempImage {
  id: string;
  file: File;
  url: string;
  altText: string;
  isPrimary: boolean;
}

const PropertyImageUpload = forwardRef<PropertyImageUploadRef, PropertyImageUploadProps>(({
  propertyId,
  images,
  onImagesChange,
  maxImages = 10,
  className = '',
}, ref) => {
  const [isUploading, setIsUploading] = useState(false);
  const [isReordering, setIsReordering] = useState(false);
  const [dragIndex, setDragIndex] = useState<number | null>(null);
  const [tempImages, setTempImages] = useState<TempImage[]>([]);
  const { toast } = useToast();

  // Upload temp images when propertyId becomes available
  useEffect(() => {
    if (propertyId && tempImages.length > 0) {
      uploadTempImages();
    }
  }, [propertyId]);

  const uploadTempImages = async () => {
    if (!propertyId || tempImages.length === 0) return;

    setIsUploading(true);

    try {
      const uploadRequest: ImageUploadRequest = {
        propertyId: propertyId,
        files: tempImages.map(temp => temp.file),
        altTexts: tempImages.map(temp => temp.altText),
        isPrimaryIndex: images.length === 0 ? 0 : undefined,
      };

      const uploadedImages = await propertyImagesService.uploadPropertyImages(propertyId, uploadRequest);
      
      if (uploadedImages.length > 0) {
        onImagesChange([...images, ...uploadedImages]);
        setTempImages([]); // Clear temp images after successful upload
        toast({
          title: "Upload successful",
          description: `${uploadedImages.length} images uploaded successfully`,
        });
      }
    } catch (error: any) {
      toast({
        title: "Upload failed",
        description: error.message || "Failed to upload images",
        variant: "destructive",
      });
      throw error; // Re-throw so parent can handle it
    } finally {
      setIsUploading(false);
    }
  };

  // Expose uploadTempImages method to parent component
  useImperativeHandle(ref, () => ({
    uploadTempImages,
  }));

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (!files.length) return;

    const totalImages = images.length + tempImages.length;
    if (totalImages + files.length > maxImages) {
      toast({
        title: "Too many images",
        description: `You can only upload up to ${maxImages} images`,
        variant: "destructive",
      });
      return;
    }

    // If we have a propertyId, upload immediately
    if (propertyId) {
      setIsUploading(true);

      try {
        // Compress images before upload
        const compressedFiles = await Promise.all(
          files.map(file => compressImage(file, 1920, 0.85))
        );

        const uploadRequest: ImageUploadRequest = {
          propertyId: propertyId,
          files: compressedFiles,
          altTexts: files.map(file => file.name),
          isPrimaryIndex: images.length === 0 ? 0 : undefined,
        };

        const uploadedImages = await propertyImagesService.uploadPropertyImages(propertyId, uploadRequest);
        
        if (uploadedImages.length > 0) {
          onImagesChange([...images, ...uploadedImages]);
          toast({
            title: "Upload successful",
            description: `${uploadedImages.length} images uploaded successfully`,
          });
        }
      } catch (error: any) {
        toast({
          title: "Upload failed",
          description: error.message || "Failed to upload images",
          variant: "destructive",
        });
      } finally {
        setIsUploading(false);
        event.target.value = '';
      }
    } else {
      // Store files temporarily if no propertyId
      try {
        const compressedFiles = await Promise.all(
          files.map(file => compressImage(file, 1920, 0.85))
        );

        const newTempImages: TempImage[] = compressedFiles.map((file, index) => ({
          id: `temp-${Date.now()}-${index}`,
          file,
          url: URL.createObjectURL(file),
          altText: file.name,
          isPrimary: images.length === 0 && tempImages.length === 0 && index === 0,
        }));

        setTempImages(prev => [...prev, ...newTempImages]);
        toast({
          title: "Images selected",
          description: `${files.length} images selected and will be uploaded when property is created`,
        });
      } catch (error: any) {
        toast({
          title: "Processing failed",
          description: "Failed to process selected images",
          variant: "destructive",
        });
      } finally {
        event.target.value = '';
      }
    }
  };

  const handleRemoveImage = async (imageId: string, index: number) => {
    if (!propertyId) {
      // Remove temp image
      setTempImages(prev => prev.filter((_, i) => i !== index));
      return;
    }

    try {
      await propertyImagesService.deleteImage(propertyId, imageId);
      const newImages = images.filter((_, i) => i !== index);
      onImagesChange(newImages);
      toast({
        title: "Image deleted",
        description: "Image deleted successfully",
      });
    } catch (error: any) {
      toast({
        title: "Delete failed",
        description: error.message || "Failed to delete image",
        variant: "destructive",
      });
    }
  };

  const handleRemoveTempImage = (index: number) => {
    setTempImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDragIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = async (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    if (dragIndex === null || dragIndex === dropIndex || !propertyId) return;

    try {
      setIsReordering(true);
      
      // Reorder images locally first for immediate feedback
      const newImages = [...images];
      const [draggedImage] = newImages.splice(dragIndex, 1);
      newImages.splice(dropIndex, 0, draggedImage);
      onImagesChange(newImages);

      // Send reorder request to server
      const reorderRequest: ReorderImagesRequest = {
        imageIds: newImages.map(img => img.id),
      };

      await propertyImagesService.reorderImages(propertyId, reorderRequest);
      
      toast({
        title: "Images reordered",
        description: "Image order updated successfully",
      });
    } catch (error: any) {
      // Revert on error
      onImagesChange(images);
      toast({
        title: "Reorder failed",
        description: error.message || "Failed to reorder images",
        variant: "destructive",
      });
    } finally {
      setIsReordering(false);
      setDragIndex(null);
    }
  };

  const allImages = [...images, ...tempImages];
  const canUploadMore = allImages.length < maxImages;

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Property Images</h3>
        <span className="text-sm text-gray-500">
          {allImages.length}/{maxImages} images
        </span>
      </div>

      {/* Image Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {allImages.map((image, index) => (
          <div 
            key={image.id || index} 
            className="relative group"
            draggable={!isUploading && !isReordering && index < images.length}
            onDragStart={(e) => handleDragStart(e, index)}
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e, index)}
          >
            <img
              src={image.url}
              alt={image.altText || `Property image ${index + 1}`}
              className="w-full h-32 object-cover rounded-lg border cursor-move"
            />
            <div className="absolute top-2 right-2 flex gap-1">
              <Button
                type="button"
                variant="destructive"
                size="sm"
                className="opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => index < images.length ? handleRemoveImage(image.id, index) : handleRemoveTempImage(index - images.length)}
                disabled={isUploading || isReordering}
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
            {image.isPrimary && (
              <div className="absolute bottom-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                Main
              </div>
            )}
            {index < images.length && (
              <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <GripVertical className="w-4 h-4 text-white drop-shadow" />
              </div>
            )}
            {dragIndex === index && (
              <div className="absolute inset-0 bg-blue-500 bg-opacity-20 border-2 border-blue-500 rounded-lg" />
            )}
            {index >= images.length && (
              <div className="absolute bottom-2 right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded">
                Pending
              </div>
            )}
          </div>
        ))}

        {/* Upload Button */}
        {canUploadMore && (
          <div className="relative">
            <input
              type="file"
              accept="image/jpeg,image/png,image/webp"
              onChange={handleFileSelect}
              multiple
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              disabled={isUploading || isReordering}
            />
            <div className="w-full h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-gray-400 transition-colors">
              {isUploading ? (
                <div className="text-center">
                  <Upload className="w-6 h-6 text-gray-400 mx-auto mb-1 animate-pulse" />
                  <span className="text-xs text-gray-500">Uploading...</span>
                </div>
              ) : (
                <div className="text-center">
                  <Plus className="w-6 h-6 text-gray-400 mx-auto mb-1" />
                  <span className="text-xs text-gray-500">Add Image</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {allImages.length === 0 && (
        <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
          <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 mb-2">No images uploaded yet</p>
          <p className="text-sm text-gray-500">
            Upload up to {maxImages} images to showcase your property
          </p>
        </div>
      )}

      <div className="flex gap-2">
        <label className="cursor-pointer">
          <input
            type="file"
            accept="image/jpeg,image/png,image/webp"
            onChange={handleFileSelect}
            multiple
            className="hidden"
            disabled={isUploading || !canUploadMore || isReordering}
          />
          <Button
            type="button"
            variant="outline"
            disabled={isUploading || !canUploadMore || isReordering}
            asChild
          >
            <span>
              <Upload className="w-4 h-4 mr-2" />
              Upload Images
            </span>
          </Button>
        </label>
      </div>

      <div className="text-xs text-gray-500">
        <p>• Supported formats: JPEG, PNG, WebP</p>
        <p>• Maximum file size: 5MB per image</p>
        <p>• First image will be used as the main property image</p>
        <p>• Drag and drop images to reorder them</p>
        {!propertyId && (
          <p className="text-yellow-600 font-medium">• Images will be uploaded when the property is created</p>
        )}
      </div>
    </div>
  );
});

PropertyImageUpload.displayName = 'PropertyImageUpload';

export default PropertyImageUpload;
