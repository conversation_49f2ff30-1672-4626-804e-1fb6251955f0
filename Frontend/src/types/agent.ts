// Database types
export type AgentApplication = Database['public']['Tables']['agent_applications']['Row'];
export type AgentCommission = Database['public']['Tables']['agent_commissions']['Row'];
export type PaymentTransaction = Database['public']['Tables']['payment_transactions']['Row'];
export type Property = Database['public']['Tables']['properties']['Row'];

// Enhanced Agent types

export interface AgentSearchFilters {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface TopAgent {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  avatar: string;
  propertyCount: number;
  totalViews: number;
  totalInquiries: number;
  conversionRate: number;
}


export interface AgentPerformanceMetrics {
  agentId: string;
  fullName: string;
  email: string | null;
  whatsappNumber: string;
  operatingAreas: string[];
  agentStatus: string;
  agentSince: string;
  
  // Property metrics
  totalProperties: number;
  availableProperties: number;
  verifiedProperties: number;
  featuredProperties: number;
  
  // Financial metrics
  monthlyEarnings: number;
  yearlyEarnings: number;
  totalEarnings: number;
  
  // Client interaction metrics
  totalInquiries: number;
  monthlyInquiries: number;
  respondedInquiries: number;
  uniqueClients: number;
  
  // Performance calculations
  responseRate: number;
  conversionRate: number;
  avgResponseTimeHours: number;
  
  // Property performance
  avgPropertyPrice: number;
  highestPropertyPrice: number;
  lowestPropertyPrice: number;
  
  // Recent activity
  lastInquiryDate: string | null;
  lastTransactionDate: string | null;
  lastPropertyUpdate: string | null;
  
  // Ratings
  avgRating: number;
  totalRatings: number;
  
  calculatedAt: string;
}

export interface AgentGoal {
  id: string;
  agentId: string;
  goalType: 'properties' | 'earnings' | 'clients' | 'conversion_rate' | 'response_time';
  targetValue: number;
  currentValue: number;
  targetDate: string;
  description: string;
  isAchieved: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ClientProfile {
  id: string;
  agentId: string;
  name: string;
  email: string;
  phone: string;
  budgetMin: number;
  budgetMax: number;
  preferredLocations: string[];
  propertyPreferences: {
    bedrooms?: number;
    bathrooms?: number;
    propertyType?: string;
    amenities?: string[];
  };
  leadScore: number;
  status: 'new' | 'contacted' | 'viewing' | 'negotiating' | 'closed' | 'lost';
  source: string;
  notes: string;
  lastContact: string | null;
  nextFollowUp: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface CommunicationHistory {
  id: string;
  clientId: string;
  agentId: string;
  type: 'call' | 'whatsapp' | 'email' | 'meeting' | 'property_viewing';
  subject: string;
  content: string;
  direction: 'inbound' | 'outbound';
  status: 'sent' | 'delivered' | 'read' | 'responded';
  scheduledAt: string | null;
  completedAt: string | null;
  createdAt: string;
}

export interface CommissionStructure {
  id: string;
  agentId: string;
  commissionType: 'percentage' | 'fixed' | 'tiered';
  baseRate: number;
  tiers?: {
    minAmount: number;
    maxAmount: number;
    rate: number;
  }[];
  propertyTypes?: string[];
  effectiveFrom: string;
  effectiveUntil: string | null;
  isActive: boolean;
  createdAt: string;
}

export interface CommissionPayment {
  id: string;
  commissionId: string;
  agentId: string;
  amount: number;
  paymentMethod: string;
  paymentReference: string;
  status: 'pending' | 'processing' | 'paid' | 'failed' | 'disputed';
  scheduledDate: string;
  paidDate: string | null;
  notes: string;
  createdAt: string;
  updatedAt: string;
}

export interface CommissionDispute {
  id: string;
  commissionId: string;
  agentId: string;
  disputeReason: string;
  disputeDetails: string;
  status: 'open' | 'investigating' | 'resolved' | 'rejected';
  resolutionNotes: string | null;
  resolvedBy: string | null;
  resolvedAt: string | null;
  createdAt: string;
}

export interface AgentVerificationStep {
  id: string;
  agentId: string;
  stepName: string;
  stepOrder: number;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';
  required: boolean;
  description: string;
  completionData: Record<string, any> | null;
  completedBy: string | null;
  completedAt: string | null;
  createdAt: string;
}

export interface AgentActivity {
  id: string;
  agentId: string;
  activityType: string;
  entityType: string | null;
  entityId: string | null;
  description: string;
  metadata: Record<string, any> | null;
  createdAt: string;
}

// Request/Response types
export interface CreateAgentProfileRequest {
  fullName: string;
  whatsappNumber: string;
  email?: string;
  operatingAreas: string[];
  residentialAddress: string;
  isRegisteredBusiness: boolean;
}

export interface UpdateAgentProfileRequest {
  fullName?: string;
  whatsappNumber?: string;
  email?: string;
  operatingAreas?: string[];
  isActive?: boolean;
}

export interface CreateClientRequest {
  name: string;
  email: string;
  phone: string;
  budgetMin: number;
  budgetMax: number;
  preferredLocations: string[];
  propertyPreferences: ClientProfile['propertyPreferences'];
  source: string;
  notes?: string;
}

export interface UpdateClientRequest {
  name?: string;
  email?: string;
  phone?: string;
  budgetMin?: number;
  budgetMax?: number;
  preferredLocations?: string[];
  propertyPreferences?: ClientProfile['propertyPreferences'];
  status?: ClientProfile['status'];
  notes?: string;
  nextFollowUp?: string;
}

export interface CreateCommunicationRequest {
  clientId: string;
  type: CommunicationHistory['type'];
  subject: string;
  content: string;
  direction: CommunicationHistory['direction'];
  scheduledAt?: string;
}

export interface AgentDashboardData {
  agent: AgentProfile;
  performance: AgentPerformanceMetrics;
  goals: AgentGoal[];
  recentClients: ClientProfile[];
  recentCommunications: CommunicationHistory[];
  pendingCommissions: AgentCommission[];
  upcomingFollowUps: ClientProfile[];
  properties: Property[];
}

export interface AgentAnalytics {
  performanceTrends: {
    date: string;
    earnings: number;
    properties: number;
    clients: number;
    conversionRate: number;
  }[];
  comparisonData: {
    rankInArea: number;
    totalAgentsInArea: number;
    percentile: number;
    topPerformers: {
      agentId: string;
      name: string;
      metricValue: number;
    }[];
  };
  goalProgress: {
    goalId: string;
    goalType: string;
    progressPercentage: number;
    daysRemaining: number;
  }[];
}

// Error types
export class AgentServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'AgentServiceError';
  }
}

// Utility types
export type AgentStatus = 'pending_review' | 'documents_reviewed' | 'referee_contacted' | 'approved' | 'rejected' | 'needs_info';
export type CommissionStatus = 'pending' | 'calculated' | 'approved' | 'paid' | 'disputed';
export type ClientStatus = 'new' | 'contacted' | 'viewing' | 'negotiating' | 'closed' | 'lost';
export type CommunicationType = 'call' | 'whatsapp' | 'email' | 'meeting' | 'property_viewing';
export type GoalType = 'properties' | 'earnings' | 'clients' | 'conversion_rate' | 'response_time';
