# Task 1 Submission Form - Agent Decision API

## Complete Task Form

### Occupation Selection
**Web Developers**
- Write supporting code for Web applications or Web sites
- Design, build, or maintain Web sites, using authoring or scripting languages, content creation tools, management tools, and digital media

---

### 📝 Request (include the overall goal, deliverable ask, and all relevant context)

You work as a backend engineer at QuickRent, a proptech startup helping real estate agents close rental deals faster. Our agents are losing deals to competitors because landlords take 1-2 days to review applications, and by then tenants have found other places. The sales team is getting complaints that our slow approval process is costing agents business. Build a REST API endpoint that allows landlords to instantly approve or deny rental applications while agents are still with prospective tenants. The endpoint should accept approve/deny decisions with optional notes, update application status in the database, and trigger real-time notifications to agents and tenants. You'll find existing agent controllers and authentication patterns in the codebase to use as reference for consistency. Make sure to include comprehensive tests since multiple agents will use this simultaneously during peak rental season.

---

### File Upload (Reference Material)

**Reference Files:**
- `agents.controller.ts` (5,839 bytes) - Existing agent controller with authentication patterns
- `agents.module.ts` (525 bytes) - Module configuration for agent endpoints
- `agents.service.ts` (6,580 bytes) - Business logic for agent operations
- `auth.controller.ts` (5,287 bytes) - Authentication controller for security patterns
- `auth.module.ts` (1,456 bytes) - Authentication module configuration
- `auth.service.ts` (13,158 bytes) - Authentication service with JWT handling

---

### 📦 Introduction to Deliverable

"Attached is the instant decision API feature for QuickRent that allows landlords to approve or deny applications in real-time during agent visits. I built the endpoint to integrate with our existing application system, added proper authentication and validation, and included comprehensive testing. The solution handles concurrent usage and sends notifications to keep agents and tenants updated on decisions. This should help our agents close deals faster and stay competitive with other platforms."

---

### 📦 Deliverable (Text)

**Solution Overview:**
- REST API endpoint for instant application decisions
- Integration with existing authentication and notification systems
- Database transactions to ensure data consistency
- Real-time notifications to agents and tenants
- Comprehensive testing for reliability under concurrent usage

**Key Features:**
- Secure endpoint with proper authentication
- Input validation and error handling
- Transaction-based updates for data integrity
- Notification system integration
- Support for concurrent agent usage

---

### Deliverable File Upload

- `decision.controller.ts` (2,456 bytes) - API controller for instant decision endpoint
- `decision.service.ts` (4,789 bytes) - Business logic service with transaction handling
- `decision.dto.ts` (1,234 bytes) - Request/response DTOs with validation
- `decision.controller.spec.ts` (3,567 bytes) - Unit tests for controller logic
- `decision.integration.spec.ts` (2,890 bytes) - Integration tests for full workflow

---

### Career Phase
**Mid-level**

### Time to Complete (Hours)
**3.0**

### How Representative Is This Task?
**5** - Building REST APIs with authentication, validation, and testing is core backend development work

### How Difficult Is This Task?
**3** - Standard API development with some complexity around notifications and concurrent usage
