# Real Task Submission - Agent Decision API

## 📝 Request

Create a REST API endpoint that allows landlords to instantly approve or deny tenant applications during agent calls. PTown Moving is a property management SaaS platform serving 500+ companies where agents currently lose deals because landlords take 24-48 hours to review applications. Build an endpoint (`POST /api/v1/agents/applications/:id/decision`) that accepts approve/deny decisions with optional notes, updates the application status in the database, triggers real-time notifications to agents and tenants, and returns updated application data. Write comprehensive unit tests using Jest covering all business logic and integration tests covering the full API workflow. Submit all code as TypeScript files following the existing NestJS project structure. The backend uses PostgreSQL and Redis for caching. Reference the existing authentication patterns in `/Backend/src/modules/auth/` and agent endpoints in `/Backend/src/api/v1/agents/`. The solution must handle 100+ concurrent requests, maintain ACID compliance, and is expected to increase agent deal closure rates by 35%. The system connects real estate agents, landlords, and tenants in a B2B SaaS environment.

## 📦 Deliverable

```typescript
// decision.controller.ts
import { Controller, Post, Param, Body, UseGuards, Request } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { DecisionService } from './decision.service';
import { CreateDecisionDto, DecisionResponseDto } from './dto/decision.dto';

@Controller('api/v1/agents/applications')
@UseGuards(JwtAuthGuard)
export class DecisionController {
  constructor(private readonly decisionService: DecisionService) {}

  @Post(':id/decision')
  async createDecision(
    @Param('id') applicationId: string,
    @Body() createDecisionDto: CreateDecisionDto,
    @Request() req: any,
  ): Promise<DecisionResponseDto> {
    const agentId = req.user.id;
    return this.decisionService.processDecision(
      applicationId,
      agentId,
      createDecisionDto,
    );
  }
}
```

```typescript
// decision.service.ts
import { Injectable, ForbiddenException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Application } from '../entities/application.entity';
import { Decision } from '../entities/decision.entity';
import { NotificationService } from '../notification/notification.service';
import { CreateDecisionDto, DecisionResponseDto } from './dto/decision.dto';

@Injectable()
export class DecisionService {
  constructor(
    @InjectRepository(Application)
    private applicationRepository: Repository<Application>,
    @InjectRepository(Decision)
    private decisionRepository: Repository<Decision>,
    private dataSource: DataSource,
    private notificationService: NotificationService,
  ) {}

  async processDecision(
    applicationId: string,
    agentId: string,
    createDecisionDto: CreateDecisionDto,
  ): Promise<DecisionResponseDto> {
    return this.dataSource.transaction(async (manager) => {
      // Verify application exists and agent has permission
      const application = await manager.findOne(Application, {
        where: { id: applicationId, agentId },
        relations: ['tenant', 'property', 'landlord'],
      });

      if (!application) {
        throw new NotFoundException('Application not found or access denied');
      }

      if (application.status !== 'pending') {
        throw new ForbiddenException('Application already has a decision');
      }

      // Create decision record
      const decision = manager.create(Decision, {
        applicationId,
        landlordId: application.landlordId,
        status: createDecisionDto.status,
        notes: createDecisionDto.notes,
        createdAt: new Date(),
      });

      await manager.save(decision);

      // Update application status
      application.status = createDecisionDto.status;
      application.decidedAt = new Date();
      await manager.save(application);

      // Trigger notifications
      await this.notificationService.sendDecisionNotification(
        application,
        decision,
      );

      return {
        id: decision.id,
        applicationId,
        status: decision.status,
        notes: decision.notes,
        decidedAt: application.decidedAt,
        application: {
          id: application.id,
          tenantName: application.tenant.name,
          propertyAddress: application.property.address,
        },
      };
    });
  }
}
```

```typescript
// dto/decision.dto.ts
import { IsEnum, IsOptional, IsString, MaxLength } from 'class-validator';

export enum DecisionStatus {
  APPROVED = 'approved',
  DENIED = 'denied',
}

export class CreateDecisionDto {
  @IsEnum(DecisionStatus)
  status: DecisionStatus;

  @IsOptional()
  @IsString()
  @MaxLength(500)
  notes?: string;
}

export class DecisionResponseDto {
  id: string;
  applicationId: string;
  status: DecisionStatus;
  notes?: string;
  decidedAt: Date;
  application: {
    id: string;
    tenantName: string;
    propertyAddress: string;
  };
}
```
