# Task 1 Submission Form - Agent Decision API

## Complete Task Form

### Occupation Selection
**Web Developers**
- Write supporting code for Web applications or Web sites
- Design, build, or maintain Web sites, using authoring or scripting languages, content creation tools, management tools, and digital media

---

### 📝 Request (include the overall goal, deliverable ask, and all relevant context)

You're a backend engineer at QuickRent, a growing proptech startup that helps real estate agents manage rental applications more efficiently. The sales team has been getting feedback from agents that they're losing deals to competitors because our application approval process is too slow. Currently, when an agent shows a property and a tenant wants to apply, the landlord has to review the application later and get back to the agent, often taking 1-2 days. By that time, the tenant has usually found another place. The CEO wants to solve this problem to help our agents close more deals and stay competitive. Build an API feature that allows landlords to make instant approve/deny decisions on applications while the agent is still with the prospective tenant. This should integrate with our existing application system and send notifications to keep everyone in the loop. The engineering team uses modern backend technologies, and you'll need to work within the existing codebase structure. Make sure the solution can handle multiple agents using it simultaneously and includes proper testing to ensure reliability.

---

### File Upload (Reference Material)

**Reference Files:**
- `agents.controller.ts` (5,839 bytes) - Existing agent controller with authentication patterns
- `agents.module.ts` (525 bytes) - Module configuration for agent endpoints
- `agents.service.ts` (6,580 bytes) - Business logic for agent operations
- `auth.controller.ts` (5,287 bytes) - Authentication controller for security patterns
- `auth.module.ts` (1,456 bytes) - Authentication module configuration
- `auth.service.ts` (13,158 bytes) - Authentication service with JWT handling

---

### 📦 Introduction to Deliverable

"Attached is the instant decision API feature for QuickRent that allows landlords to approve or deny applications in real-time during agent visits. I built the endpoint to integrate with our existing application system, added proper authentication and validation, and included comprehensive testing. The solution handles concurrent usage and sends notifications to keep agents and tenants updated on decisions. This should help our agents close deals faster and stay competitive with other platforms."

---

### 📦 Deliverable (Text)

**Solution Overview:**
- REST API endpoint for instant application decisions
- Integration with existing authentication and notification systems
- Database transactions to ensure data consistency
- Real-time notifications to agents and tenants
- Comprehensive testing for reliability under concurrent usage

**Key Features:**
- Secure endpoint with proper authentication
- Input validation and error handling
- Transaction-based updates for data integrity
- Notification system integration
- Support for concurrent agent usage

---

### Deliverable File Upload

- `decision.controller.ts` (2,456 bytes) - API controller for instant decision endpoint
- `decision.service.ts` (4,789 bytes) - Business logic service with transaction handling
- `decision.dto.ts` (1,234 bytes) - Request/response DTOs with validation
- `decision.controller.spec.ts` (3,567 bytes) - Unit tests for controller logic
- `decision.integration.spec.ts` (2,890 bytes) - Integration tests for full workflow

---

### Career Phase
**Mid-level**

### Time to Complete (Hours)
**3.0**

### How Representative Is This Task?
**5** - Building REST APIs with authentication, validation, and testing is core backend development work

### How Difficult Is This Task?
**3** - Standard API development with some complexity around notifications and concurrent usage
