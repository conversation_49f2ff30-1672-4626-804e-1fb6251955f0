# Real Task Submission - Frontend Environment Setup Guide

## 📝 Request

Write a comprehensive frontend environment setup guide to reduce onboarding time for new developers from 2+ days to under 4 hours. PTown Moving's engineering team is growing from 15 to 18 developers with 3 new hires starting next month, and the current setup process causes frustration and delays productivity. Create a detailed Markdown guide at `/Frontend/docs/ENVIRONMENT_SETUP.md` covering system prerequisites including specific Node.js versions and package managers, step-by-step installation instructions with exact commands, environment variable configuration with examples, common troubleshooting scenarios and solutions, and validation steps to confirm successful setup. Create a companion validation script at `scripts/validate-environment.sh` to verify setup automatically. Test the guide with a new team member and incorporate their feedback. Include screenshots for complex setup steps. The tech stack uses React 18, TypeScript 5.x, Vite 4.x, Tailwind CSS, Jest, and Cypress. The target audience is junior to mid-level frontend developers with varying experience levels. This will save 18+ hours of developer time across the new hires and improve the onboarding experience.

## 📦 Deliverable

```markdown
# Frontend Environment Setup Guide

## Prerequisites

### System Requirements
- **Operating System**: macOS 12+, Windows 10+, or Ubuntu 20.04+
- **Memory**: Minimum 8GB RAM (16GB recommended)
- **Storage**: At least 5GB free space
- **Internet**: Stable broadband connection

### Required Software Versions
- **Node.js**: Version 18.17.0 or higher (LTS recommended)
- **npm**: Version 9.0.0+ (comes with Node.js)
- **Git**: Version 2.30.0+

## Installation Steps

### 1. Install Node.js and npm

#### macOS (using Homebrew)
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Node.js (includes npm)
brew install node@18

# Verify installation
node --version  # Should show v18.17.0 or higher
npm --version   # Should show 9.0.0 or higher
```

#### Windows
1. Download Node.js installer from [nodejs.org](https://nodejs.org/)
2. Choose "18.17.0 LTS" version
3. Run installer with default settings
4. Open Command Prompt and verify:
```cmd
node --version
npm --version
```

#### Ubuntu/Linux
```bash
# Update package index
sudo apt update

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

### 2. Clone Repository

```bash
# Clone the repository
git clone https://github.com/ptownmoving/frontend.git
cd frontend

# Verify you're in the correct directory
pwd  # Should end with /frontend
ls   # Should see package.json, src/, public/ directories
```

### 3. Install Dependencies

```bash
# Install all project dependencies
npm install

# This may take 2-3 minutes on first run
# You should see "added XXX packages" when complete
```

### 4. Environment Configuration

#### Create Environment File
```bash
# Copy the example environment file
cp .env.example .env.local

# Open the file for editing
# macOS: open .env.local
# Windows: notepad .env.local
# Linux: nano .env.local
```

#### Required Environment Variables
```bash
# .env.local file contents
VITE_API_BASE_URL=http://localhost:3001/api/v1
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key_here
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_key_here
VITE_ENVIRONMENT=development
VITE_SENTRY_DSN=your_sentry_dsn_here
```

**Important**: Request actual API keys from your team lead for:
- Stripe (for payment testing)
- Google Maps (for property locations)
- Sentry (for error tracking)

### 5. Start Development Server

```bash
# Start the development server
npm run dev

# You should see output like:
# ➜  Local:   http://localhost:5173/
# ➜  Network: use --host to expose
```

Open [http://localhost:5173](http://localhost:5173) in your browser. You should see the PTown Moving homepage.

## Validation Steps

### Automated Validation
Run the validation script to check your setup:
```bash
# Make script executable (macOS/Linux)
chmod +x scripts/validate-environment.sh

# Run validation
./scripts/validate-environment.sh
```

### Manual Validation Checklist
- [ ] Node.js version 18.17.0+
- [ ] npm version 9.0.0+
- [ ] All dependencies installed without errors
- [ ] Development server starts on port 5173
- [ ] Homepage loads without console errors
- [ ] Hot reload works (edit a file and see changes)
- [ ] Tests run successfully: `npm test`
- [ ] Build completes: `npm run build`

## Common Issues & Solutions

### Issue: "node: command not found"
**Solution**: Node.js not installed or not in PATH
```bash
# Verify installation
which node
# If empty, reinstall Node.js following steps above
```

### Issue: "npm install" fails with permission errors
**Solution**: Fix npm permissions
```bash
# macOS/Linux
sudo chown -R $(whoami) ~/.npm
npm cache clean --force

# Windows (run as Administrator)
npm cache clean --force
```

### Issue: Port 5173 already in use
**Solution**: Kill existing process or use different port
```bash
# Kill process on port 5173
lsof -ti:5173 | xargs kill -9

# Or start on different port
npm run dev -- --port 5174
```

### Issue: "Module not found" errors
**Solution**: Clear cache and reinstall
```bash
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

### Issue: TypeScript errors in IDE
**Solution**: Restart TypeScript service
- **VS Code**: Cmd/Ctrl + Shift + P → "TypeScript: Restart TS Server"
- **WebStorm**: File → Invalidate Caches and Restart

## Development Workflow

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm test             # Run unit tests
npm run test:e2e     # Run Cypress tests
npm run lint         # Check code style
npm run type-check   # Check TypeScript types
```

### Recommended VS Code Extensions
- ES7+ React/Redux/React-Native snippets
- TypeScript Importer
- Tailwind CSS IntelliSense
- Prettier - Code formatter
- ESLint

## Getting Help

### Internal Resources
- **Slack**: #frontend-help channel
- **Documentation**: `/Frontend/docs/` directory
- **Team Lead**: @sarah.chen (Slack)

### External Resources
- [React Documentation](https://react.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Vite Guide](https://vitejs.dev/guide/)
- [Tailwind CSS Docs](https://tailwindcss.com/docs)

---

**Setup Time Target**: Under 4 hours  
**Last Updated**: January 2024  
**Tested By**: New hire feedback incorporated
```

```bash
#!/bin/bash
# scripts/validate-environment.sh - Environment validation script

echo "🔍 PTown Moving Frontend Environment Validation"
echo "=============================================="

# Check Node.js version
echo "📦 Checking Node.js version..."
NODE_VERSION=$(node --version 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ Node.js: $NODE_VERSION"
    # Check if version is 18+
    MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
    if [ "$MAJOR_VERSION" -ge 18 ]; then
        echo "✅ Node.js version is compatible"
    else
        echo "❌ Node.js version must be 18+ (current: $NODE_VERSION)"
        exit 1
    fi
else
    echo "❌ Node.js not found. Please install Node.js 18+"
    exit 1
fi

# Check npm version
echo "📦 Checking npm version..."
NPM_VERSION=$(npm --version 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ npm: v$NPM_VERSION"
else
    echo "❌ npm not found"
    exit 1
fi

# Check if in correct directory
echo "📁 Checking project directory..."
if [ -f "package.json" ] && [ -d "src" ]; then
    echo "✅ In correct project directory"
else
    echo "❌ Not in frontend project directory. Run from project root."
    exit 1
fi

# Check dependencies
echo "📦 Checking dependencies..."
if [ -d "node_modules" ]; then
    echo "✅ Dependencies installed"
else
    echo "⚠️  Dependencies not installed. Run 'npm install'"
fi

# Check environment file
echo "🔧 Checking environment configuration..."
if [ -f ".env.local" ]; then
    echo "✅ Environment file exists"
else
    echo "⚠️  .env.local not found. Copy from .env.example"
fi

# Test development server start
echo "🚀 Testing development server..."
timeout 10s npm run dev > /dev/null 2>&1 &
SERVER_PID=$!
sleep 5

if kill -0 $SERVER_PID 2>/dev/null; then
    echo "✅ Development server starts successfully"
    kill $SERVER_PID
else
    echo "❌ Development server failed to start"
fi

# Run tests
echo "🧪 Running tests..."
npm test -- --run --reporter=basic > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Tests pass"
else
    echo "⚠️  Some tests failing - check with team"
fi

echo ""
echo "🎉 Environment validation complete!"
echo "If all items show ✅, you're ready to develop!"
```
