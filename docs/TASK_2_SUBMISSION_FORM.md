# Task 2 Submission Form - Escrow Double-Charge Bugfix

## Complete Task Form

### Occupation Selection
**Web Developers**
- Write supporting code for Web applications or Web sites
- Design, build, or maintain Web sites, using authoring or scripting languages, content creation tools, management tools, and digital media

---

### 📝 Request (include the overall goal, deliverable ask, and all relevant context)

You work as a backend engineer at RentFlow, a B2B SaaS platform processing $50M+ annually in security deposit payments for property management companies. The finance team has escalated a critical issue where some tenants are being charged twice for the same security deposit, causing customer complaints and threatening client relationships. The CFO is concerned about potential chargebacks and regulatory scrutiny. Debug and fix the duplicate charging issue in our escrow payment system. Investigate what's causing the duplicate charges, implement a solution to prevent it from happening again, and document your findings in a post-mortem for the engineering team. The payment processing uses Stripe integration with PostgreSQL for transaction records. You'll need to dig into the payments module to find the root cause and add regression tests to catch similar issues in the future.

---

### File Upload (Reference Material)

**Reference Files:**
- `payment.controller.ts` (4,231 bytes) - Current payment controller with escrow endpoints
- `escrow.service.ts` (3,892 bytes) - Existing escrow service implementation
- `stripe.service.ts` (2,156 bytes) - Stripe integration service
- `escrow-transaction.entity.ts` (1,445 bytes) - Database entity for escrow transactions
- `payment-status.enum.ts` (287 bytes) - Payment status enumeration

---

### 📦 Introduction to Deliverable

"Attached is my investigation and fix for the duplicate escrow charges affecting RentFlow customers. I discovered the root cause was a race condition in our payment processing that occurred when multiple requests hit the system simultaneously. The fix includes updated service code with proper transaction locking, comprehensive tests to prevent regression, and a detailed post-mortem document explaining what happened and how we're preventing it going forward. This should resolve the customer complaints and protect us from future chargeback risks."

---

### 📦 Deliverable (Text)

**Root Cause**: Race condition in escrow payment processing where concurrent requests could bypass duplicate checks due to lack of database locking.

**Key Fixes**:
1. Database locking with pessimistic write locks
2. Stripe idempotency keys for payment deduplication
3. Enhanced validation and conflict resolution
4. Comprehensive regression testing

**Impact**: Prevents future duplicate charges, protects customer trust, eliminates chargeback risks.

---

### Deliverable File Upload

- `escrow.service.ts` (5,847 bytes) - Fixed service with transaction locking
- `payment.controller.ts` (4,892 bytes) - Updated controller with error handling
- `escrow-regression.spec.ts` (3,234 bytes) - Regression test suite
- `escrow-bugfix-postmortem.md` (2,891 bytes) - Post-mortem analysis
- `payment-monitoring.service.ts` (1,567 bytes) - Enhanced monitoring

---

### Career Phase
**Mid-level**

### Time to Complete (Hours)
**2.0**

### How Representative Is This Task?
**4** - Production bug fixes with concurrency issues are common backend work

### How Difficult Is This Task?
**4** - Complex debugging involving race conditions and payment systems
