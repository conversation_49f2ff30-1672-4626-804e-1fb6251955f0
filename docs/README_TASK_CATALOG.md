# Web Development Task Catalog

*Think of This Like:*

• *Request = What your manager/client/team asked you to do.*
• *Deliverable = The actual thing you created in response.*
• You're showing how a real task starts and ends — useful for teaching an AI how to help others with similar jobs.

---

## ✅ What You Need to Submit

### 1. *Occupation*

**Software Engineer – Web Development**

---

### 2. *Associated Actions*

**Primary Actions Covered in This Catalog:**

• **Build features** - API endpoints, UI components, marketplace functionality
• **Fix bugs** - Debug payment issues, resolve race conditions
• **Write documentation** - Environment setup guides, performance reports
• **Analyze performance** - Profile APIs, optimize database queries
• **Debug backend features** - Payment flows, authentication systems
• **Deploy and test** - Staging deployments, end-to-end testing

---

### 3. 📝 *Request (what you were asked to do)*

This is the instruction you received. Make sure it includes:

• *Goal* – Why are you doing this?
• *Deliverable Ask* – What exactly do you need to produce? In what format?
• *Context* – Anything someone else would need to complete this. Think of links, login info, API docs, or tech constraints.

---

## Task Examples

### Task 1: Implement Agent–Landlord Instant Decision API

**Associated Action:** Build backend features

*Goal*: You work as a backend engineer at PTown Moving, a property management SaaS platform that connects real estate agents, landlords, and tenants. Currently, landlords take 24-48 hours to review tenant applications, causing agents to lose deals to competitors. Build an instant decision API that allows landlords to approve/deny applications in real-time during agent calls, reducing deal closure time from days to minutes.

*Deliverable Ask*:
- Create a REST API endpoint (`POST /api/v1/agents/applications/:id/decision`) that:
  - Accepts decision payload (approve/deny + optional notes)
  - Updates application status in database
  - Triggers real-time notifications to agent and tenant
  - Returns updated application data
- Write comprehensive unit tests (Jest) covering all business logic
- Write integration tests covering the full API workflow
- Submit all code as TypeScript files following existing project structure

*Context*:
- **Company**: PTown Moving - B2B SaaS serving 500+ property management companies
- **Role**: Backend Engineer on the Agent Tools team
- **Tech Stack**: NestJS backend with PostgreSQL, Redis for caching
- **Existing Code**: Authentication in `/Backend/src/modules/auth/`, agent endpoints in `/Backend/src/api/v1/agents/`
- **Requirements**: Must handle 100+ concurrent requests, maintain ACID compliance
- **Business Impact**: Expected to increase agent deal closure rate by 35%

---

### Task 2: Debug Escrow Double-Charge Bug

**Associated Action:** Fix bugs

*Goal*: You're a backend engineer at PTown Moving where tenants pay security deposits through our escrow system. A critical production bug is causing some tenants to be charged twice for the same deposit, resulting in customer complaints and potential chargebacks. The finance team reports 12 affected transactions in the last week, totaling $18,000 in duplicate charges. Fix this immediately to prevent further customer impact and financial liability.

*Deliverable Ask*:
- Identify the root cause of the double-charging bug in the escrow payment flow
- Implement a fix that prevents duplicate charges while maintaining payment reliability
- Create comprehensive regression tests to prevent this issue from recurring
- Write a detailed post-mortem document (Markdown format) including:
  - Root cause analysis with code examples
  - Timeline of the incident
  - Prevention measures implemented
  - Monitoring improvements added

*Context*:
- **Company**: PTown Moving - Processing $2M+ monthly in escrow payments
- **Role**: Backend Engineer on the Payments team
- **Urgency**: Critical P0 bug affecting customer billing and company reputation
- **Tech Stack**: NestJS with Stripe integration, PostgreSQL for transaction records
- **Existing Code**: Payment logic in `/Backend/src/modules/payments/` and `/Backend/src/api/v1/payments/`
- **Impact**: 12 customers affected, $18K duplicate charges, potential chargeback fees
- **Compliance**: Must maintain PCI DSS compliance and audit trail integrity

---

### Task 3: Write Frontend Environment Setup Guide

**Associated Action:** Write documentation

*Goal*: You're a frontend engineer at PTown Moving where new developers are struggling with environment setup, taking 2+ days to get productive. The engineering manager wants to reduce onboarding time to under 4 hours. With 3 new hires starting next month, creating a comprehensive setup guide will save 18+ hours of developer time and reduce frustration for new team members.

*Deliverable Ask*:
- Write a comprehensive Markdown guide (`/Frontend/docs/ENVIRONMENT_SETUP.md`) covering:
  - System prerequisites (Node.js versions, package managers)
  - Step-by-step installation instructions with commands
  - Environment variable configuration with examples
  - Common troubleshooting scenarios and solutions
  - Validation steps to confirm successful setup
- Create a companion validation script (`scripts/validate-environment.sh`) to verify setup
- Test the guide with a new team member and incorporate their feedback
- Include screenshots for complex setup steps

*Context*:
- **Company**: PTown Moving - Growing engineering team (15 developers, 3 new hires coming)
- **Role**: Frontend Engineer on the Developer Experience team
- **Tech Stack**: React 18, TypeScript 5.x, Vite 4.x, Tailwind CSS, Jest, Cypress
- **Current Problem**: New developers spend 2+ days on environment setup vs. 4-hour target
- **Business Impact**: Faster onboarding = quicker time to productivity for new hires
- **Audience**: Junior to mid-level frontend developers with varying experience levels

---

### Task 4: Optimize Property Search API Performance

**Associated Action:** Analyze performance

*Goal*: You're a senior backend engineer at PTown Moving where the property search API is becoming a bottleneck. During peak hours (6-9 PM), response times spike to 2.5+ seconds with 1000+ concurrent users, causing agents to lose leads due to slow search results. The product team reports a 15% drop in search engagement when response times exceed 1 second. Optimize the search API to handle peak load while maintaining sub-500ms response times.

*Deliverable Ask*:
- Profile the `/api/v1/properties/search` endpoint to identify performance bottlenecks
- Implement optimizations including:
  - Database query optimization and indexing strategies
  - Redis caching layer for frequent searches
  - Query result pagination improvements
- Create a comprehensive performance report (Markdown) documenting:
  - Before/after metrics with load test results
  - Specific optimizations implemented with code examples
  - Monitoring recommendations for ongoing performance tracking
  - Scalability projections for future growth
- Provide database migration files for any schema changes

*Context*:
- **Company**: PTown Moving - Serving 50,000+ property searches daily across 500+ markets
- **Role**: Senior Backend Engineer on the Platform Performance team
- **Current Performance**: 2.5s response time at 1000 concurrent users (target: <500ms)
- **Business Impact**: 15% engagement drop when searches exceed 1s response time
- **Tech Stack**: NestJS, PostgreSQL 14, TypeORM, Redis, AWS RDS with read replicas
- **Database**: 2M+ property records with complex filtering (location, price, amenities)
- **Peak Traffic**: 6-9 PM daily with 1000+ concurrent search requests

---

### Task 5: Launch Service Bundle Marketplace Feature

**Associated Action:** Deploy and test

*Goal*: You're a full-stack engineer at PTown Moving launching a new revenue stream. The company wants to offer bundled services (cleaning, WiFi setup, interior decor) to tenants moving into properties, projected to generate $500K additional annual revenue. The marketplace feature has been developed and needs final testing and deployment to staging before next sprint's production launch. Ensure the feature works flawlessly across all user flows to avoid launch delays.

*Deliverable Ask*:
- Deploy the service bundle marketplace UI to staging environment
- Write comprehensive end-to-end Cypress tests covering:
  - Service browsing and filtering
  - Bundle selection and customization
  - Checkout flow with payment processing
  - Booking confirmation and scheduling
  - Error handling and edge cases
- Create a detailed release note (Markdown) including:
  - Feature overview and business value
  - User-facing changes and new capabilities
  - Technical implementation highlights
  - Known limitations and future enhancements
- Validate staging deployment with QA team and stakeholders

*Context*:
- **Company**: PTown Moving - Expanding from property management to service marketplace
- **Role**: Full-Stack Engineer on the Growth team
- **Business Impact**: $500K projected annual revenue from service commissions
- **Tech Stack**: React frontend, NestJS backend, Stripe for payments, AWS deployment
- **Existing Code**: Marketplace components in `/Frontend/src/components/marketplace/`
- **Timeline**: Must be staging-ready for next sprint's production launch
- **Stakeholders**: Product, QA, and business teams need to validate before launch

---

### 4. 📦 *Deliverable (the thing you produced)*

Submit:

• A *real* output (code, report, doc, audit, deck, etc.)
• If it's code, upload the file or paste the text
• Write an *intro* like:

"Attached is the mobile performance audit for `/pricing`, `/features`, and `/signup`. I used Lighthouse and WebPageTest. The document includes key metrics, screenshots, and prioritized recommendations."

---

## Deliverables

### Task 1: Agent Decision API
*Intro*: "Attached is the REST API implementation for instant agent-landlord decision making at PTown Moving. I created the endpoint with proper authentication, validation, and comprehensive test coverage. The implementation handles concurrent requests and maintains data consistency while providing real-time notifications to improve deal closure rates."

**Deliverable Files:**
- [decision.controller.ts](Backend/src/api/v1/agents/decision.controller.ts) - Main API endpoint controller
- [decision.service.ts](Backend/src/api/v1/agents/decision.service.ts) - Business logic service
- [decision.dto.ts](Backend/src/api/v1/agents/dto/decision.dto.ts) - Request/response DTOs
- [decision.controller.spec.ts](Backend/src/api/v1/agents/tests/decision.controller.spec.ts) - Unit tests
- [decision.integration.spec.ts](Backend/src/api/v1/agents/tests/decision.integration.spec.ts) - Integration tests

### Task 2: Escrow Bugfix
*Intro*: "Attached is the comprehensive bugfix for the critical escrow double-charge issue affecting PTown Moving customers. I identified the race condition in concurrent payment processing, implemented proper transaction locking, and added extensive regression tests. The post-mortem document provides detailed analysis and prevention measures to avoid similar issues."

**Deliverable Files:**
- [escrow.service.ts](Backend/src/modules/payments/escrow.service.ts) - Fixed service with transaction locking
- [payment.controller.ts](Backend/src/api/v1/payments/payment.controller.ts) - Updated controller with validation
- [escrow-regression.spec.ts](Backend/src/modules/payments/tests/escrow-regression.spec.ts) - Comprehensive regression tests
- [escrow-bugfix-postmortem.md](docs/incidents/escrow-bugfix-postmortem.md) - Detailed post-mortem analysis
- [payment-monitoring.ts](Backend/src/modules/payments/monitoring/payment-monitoring.ts) - Enhanced monitoring

### Task 3: Environment Setup Guide
*Intro*: "Attached is the comprehensive frontend environment setup guide for PTown Moving's development team. I created detailed documentation covering all prerequisites, step-by-step installation, environment configuration, and troubleshooting. The guide includes a validation script and has been tested with a new team member to ensure clarity and completeness."

**Deliverable Files:**
- [ENVIRONMENT_SETUP.md](Frontend/docs/ENVIRONMENT_SETUP.md) - Complete setup guide with screenshots
- [validate-environment.sh](scripts/validate-environment.sh) - Automated setup validation script
- [environment-guide-review.md](docs/onboarding/environment-guide-review.md) - New hire feedback and improvements
- [setup-troubleshooting.md](Frontend/docs/setup-troubleshooting.md) - Common issues and solutions
- [.env.example](Frontend/.env.example) - Updated environment variable template

### Task 4: Search Performance Optimization
*Intro*: "Attached is the comprehensive performance optimization for PTown Moving's property search API. I profiled the endpoint, identified critical bottlenecks, implemented database indexing and Redis caching, and achieved sub-500ms response times under peak load. The performance report documents all optimizations with before/after metrics and scalability projections."

**Deliverable Files:**
- [search.service.ts](Backend/src/api/v1/properties/search.service.ts) - Optimized search service with caching
- [search.controller.ts](Backend/src/api/v1/properties/search.controller.ts) - Updated controller with pagination
- [property-search-performance.md](docs/performance/property-search-performance.md) - Comprehensive performance report
- [add-search-indexes.sql](Backend/database/migrations/add-search-indexes.sql) - Database optimization migration
- [search-cache.service.ts](Backend/src/modules/cache/search-cache.service.ts) - Redis caching implementation
- [search-load-test.js](tests/performance/search-load-test.js) - Load testing script and results

### Task 5: Marketplace Feature Launch
*Intro*: "Attached is the complete service bundle marketplace feature launch for PTown Moving's new revenue stream. I successfully deployed the UI to staging, wrote comprehensive end-to-end tests covering all user flows, and prepared detailed release documentation. The feature is validated and ready for production launch with projected $500K annual revenue impact."

**Deliverable Files:**
- [marketplace.cy.ts](Frontend/tests/e2e/marketplace.cy.ts) - Comprehensive Cypress E2E tests
- [marketplace-release-note.md](docs/releases/marketplace-release-note.md) - Detailed release documentation
- [staging-deployment.log](deployments/staging/marketplace-deployment.log) - Staging deployment verification
- [marketplace-validation.md](docs/qa/marketplace-validation.md) - QA validation results and sign-off
- [service-booking.cy.ts](Frontend/tests/e2e/service-booking.cy.ts) - Service booking flow tests
- [payment-integration.cy.ts](Frontend/tests/e2e/payment-integration.cy.ts) - Payment processing tests

---

### 5. 🕓 Time to Complete

| Task | Time (hours) |
|------|--------------|
| 1    | 3.0          |
| 2    | 2.0          |
| 3    | 1.5          |
| 4    | 2.5          |
| 5    | 3.0          |

---

### 6. 🎯 Career Phase

| Task | Phase  |
|------|--------|
| 1    | mid    |
| 2    | mid    |
| 3    | early  |
| 4    | senior |
| 5    | mid    |

---

### 7. 🔁 How Representative Is This Task?

| Task | Scale (1–5) |
|------|-------------|
| 1    | 5           |
| 2    | 4           |
| 3    | 3           |
| 4    | 4           |
| 5    | 5           |

---

### 8. 🔥 How Difficult Is This Task?

| Task | Scale (1–5) | Reasoning |
|------|-------------|-----------|
| 1    | 3           | Standard API development with concurrency and notification requirements |
| 2    | 4           | Critical production debugging with financial impact and compliance requirements |
| 3    | 2           | Documentation task requiring user testing and validation |
| 4    | 5           | Complex performance optimization with database, caching, and scalability concerns |
| 5    | 4           | Full-stack feature deployment with payment integration and comprehensive testing |

---

## 📊 **Task Summary Metrics**

| Metric | Average | Range |
|--------|---------|-------|
| **Time to Complete** | 2.4 hours | 1.5 - 3.0 hours |
| **Difficulty** | 3.6/5 | 2 - 5 |
| **Representativeness** | 4.2/5 | 3 - 5 |

**Career Phase Distribution:**
- Early: 20% (1 task)
- Mid: 60% (3 tasks)
- Senior: 20% (1 task)

---

**End of Catalog**