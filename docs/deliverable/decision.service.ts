import { Injectable, ForbiddenException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Application } from '../entities/application.entity';
import { Decision } from '../entities/decision.entity';
import { NotificationService } from '../notification/notification.service';
import { CreateDecisionDto, DecisionResponseDto } from './dto/decision.dto';

@Injectable()
export class DecisionService {
  constructor(
    @InjectRepository(Application)
    private applicationRepository: Repository<Application>,
    @InjectRepository(Decision)
    private decisionRepository: Repository<Decision>,
    private dataSource: DataSource,
    private notificationService: NotificationService,
  ) {}

  async processDecision(
    applicationId: string,
    agentId: string,
    createDecisionDto: CreateDecisionDto,
  ): Promise<DecisionResponseDto> {
    return this.dataSource.transaction(async (manager) => {
      // Verify application exists and agent has permission
      const application = await manager.findOne(Application, {
        where: { id: applicationId, agentId },
        relations: ['tenant', 'property', 'landlord'],
      });

      if (!application) {
        throw new NotFoundException('Application not found or access denied');
      }

      if (application.status !== 'pending') {
        throw new ForbiddenException('Application already has a decision');
      }

      // Create decision record
      const decision = manager.create(Decision, {
        applicationId,
        landlordId: application.landlordId,
        status: createDecisionDto.status,
        notes: createDecisionDto.notes,
        createdAt: new Date(),
      });

      await manager.save(decision);

      // Update application status
      application.status = createDecisionDto.status;
      application.decidedAt = new Date();
      await manager.save(application);

      // Trigger notifications
      await this.notificationService.sendDecisionNotification(
        application,
        decision,
      );

      return {
        id: decision.id,
        applicationId,
        status: decision.status,
        notes: decision.notes,
        decidedAt: application.decidedAt,
        application: {
          id: application.id,
          tenantName: application.tenant.name,
          propertyAddress: application.property.address,
        },
      };
    });
  }
}
