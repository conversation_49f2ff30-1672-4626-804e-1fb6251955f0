import { Test, TestingModule } from '@nestjs/testing';
import { DecisionController } from './decision.controller';
import { DecisionService } from './decision.service';
import { CreateDecisionDto, DecisionStatus } from './dto/decision.dto';

describe('DecisionController', () => {
  let controller: DecisionController;
  let service: DecisionService;

  const mockDecisionService = {
    processDecision: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DecisionController],
      providers: [
        {
          provide: DecisionService,
          useValue: mockDecisionService,
        },
      ],
    }).compile();

    controller = module.get<DecisionController>(DecisionController);
    service = module.get<DecisionService>(DecisionService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createDecision', () => {
    const mockRequest = {
      user: { id: 'agent-123' },
    };

    const createDecisionDto: CreateDecisionDto = {
      status: DecisionStatus.APPROVED,
      notes: 'Great tenant, approved immediately',
    };

    const expectedResponse = {
      id: 'decision-456',
      applicationId: 'app-789',
      status: DecisionStatus.APPROVED,
      notes: 'Great tenant, approved immediately',
      decidedAt: new Date(),
      application: {
        id: 'app-789',
        tenantName: 'John Doe',
        propertyAddress: '123 Main St',
      },
    };

    it('should create a decision successfully', async () => {
      mockDecisionService.processDecision.mockResolvedValue(expectedResponse);

      const result = await controller.createDecision(
        'app-789',
        createDecisionDto,
        mockRequest,
      );

      expect(service.processDecision).toHaveBeenCalledWith(
        'app-789',
        'agent-123',
        createDecisionDto,
      );
      expect(result).toEqual(expectedResponse);
    });

    it('should handle service errors', async () => {
      const error = new Error('Application not found');
      mockDecisionService.processDecision.mockRejectedValue(error);

      await expect(
        controller.createDecision('app-789', createDecisionDto, mockRequest),
      ).rejects.toThrow('Application not found');

      expect(service.processDecision).toHaveBeenCalledWith(
        'app-789',
        'agent-123',
        createDecisionDto,
      );
    });

    it('should pass agent ID from request to service', async () => {
      const customRequest = { user: { id: 'different-agent' } };
      mockDecisionService.processDecision.mockResolvedValue(expectedResponse);

      await controller.createDecision(
        'app-789',
        createDecisionDto,
        customRequest,
      );

      expect(service.processDecision).toHaveBeenCalledWith(
        'app-789',
        'different-agent',
        createDecisionDto,
      );
    });

    it('should handle denial decisions', async () => {
      const denialDto: CreateDecisionDto = {
        status: DecisionStatus.DENIED,
        notes: 'Insufficient income verification',
      };

      const denialResponse = {
        ...expectedResponse,
        status: DecisionStatus.DENIED,
        notes: 'Insufficient income verification',
      };

      mockDecisionService.processDecision.mockResolvedValue(denialResponse);

      const result = await controller.createDecision(
        'app-789',
        denialDto,
        mockRequest,
      );

      expect(result.status).toBe(DecisionStatus.DENIED);
      expect(result.notes).toBe('Insufficient income verification');
    });
  });
});
