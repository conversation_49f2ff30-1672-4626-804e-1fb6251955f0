import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { DecisionController } from './decision.controller';
import { DecisionService } from './decision.service';
import { Application } from '../entities/application.entity';
import { Decision } from '../entities/decision.entity';
import { NotificationService } from '../notification/notification.service';
import { DecisionStatus } from './dto/decision.dto';

describe('Decision Integration Tests', () => {
  let app: INestApplication;
  let applicationRepository: any;
  let decisionRepository: any;
  let dataSource: any;
  let notificationService: any;

  const mockApplication = {
    id: 'app-123',
    agentId: 'agent-456',
    landlordId: 'landlord-789',
    status: 'pending',
    tenant: { name: '<PERSON>' },
    property: { address: '123 Main St' },
    landlord: { id: 'landlord-789' },
  };

  const mockDecision = {
    id: 'decision-123',
    applicationId: 'app-123',
    landlordId: 'landlord-789',
    status: DecisionStatus.APPROVED,
    notes: 'Approved',
    createdAt: new Date(),
  };

  beforeEach(async () => {
    applicationRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
      create: jest.fn(),
    };

    decisionRepository = {
      save: jest.fn(),
      create: jest.fn(),
    };

    dataSource = {
      transaction: jest.fn(),
    };

    notificationService = {
      sendDecisionNotification: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [DecisionController],
      providers: [
        DecisionService,
        {
          provide: getRepositoryToken(Application),
          useValue: applicationRepository,
        },
        {
          provide: getRepositoryToken(Decision),
          useValue: decisionRepository,
        },
        {
          provide: DataSource,
          useValue: dataSource,
        },
        {
          provide: NotificationService,
          useValue: notificationService,
        },
      ],
    }).compile();

    app = module.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
    jest.clearAllMocks();
  });

  describe('POST /api/v1/agents/applications/:id/decision', () => {
    const validDecisionDto = {
      status: DecisionStatus.APPROVED,
      notes: 'Great tenant, approved immediately',
    };

    it('should successfully create a decision', async () => {
      // Mock transaction callback
      dataSource.transaction.mockImplementation(async (callback) => {
        const manager = {
          findOne: jest.fn().mockResolvedValue(mockApplication),
          create: jest.fn().mockReturnValue(mockDecision),
          save: jest.fn().mockResolvedValue(mockDecision),
        };
        return callback(manager);
      });

      notificationService.sendDecisionNotification.mockResolvedValue(undefined);

      const response = await request(app.getHttpServer())
        .post('/api/v1/agents/applications/app-123/decision')
        .send(validDecisionDto)
        .expect(201);

      expect(response.body).toMatchObject({
        id: expect.any(String),
        applicationId: 'app-123',
        status: DecisionStatus.APPROVED,
        notes: 'Great tenant, approved immediately',
      });

      expect(notificationService.sendDecisionNotification).toHaveBeenCalled();
    });

    it('should return 404 when application not found', async () => {
      dataSource.transaction.mockImplementation(async (callback) => {
        const manager = {
          findOne: jest.fn().mockResolvedValue(null),
        };
        return callback(manager);
      });

      await request(app.getHttpServer())
        .post('/api/v1/agents/applications/nonexistent/decision')
        .send(validDecisionDto)
        .expect(404);
    });

    it('should return 403 when application already decided', async () => {
      const decidedApplication = {
        ...mockApplication,
        status: 'approved',
      };

      dataSource.transaction.mockImplementation(async (callback) => {
        const manager = {
          findOne: jest.fn().mockResolvedValue(decidedApplication),
        };
        return callback(manager);
      });

      await request(app.getHttpServer())
        .post('/api/v1/agents/applications/app-123/decision')
        .send(validDecisionDto)
        .expect(403);
    });

    it('should validate request body', async () => {
      const invalidDto = {
        status: 'invalid-status',
        notes: 'A'.repeat(501), // Exceeds max length
      };

      await request(app.getHttpServer())
        .post('/api/v1/agents/applications/app-123/decision')
        .send(invalidDto)
        .expect(400);
    });

    it('should handle denial decisions', async () => {
      const denialDto = {
        status: DecisionStatus.DENIED,
        notes: 'Insufficient income verification',
      };

      const deniedDecision = {
        ...mockDecision,
        status: DecisionStatus.DENIED,
        notes: 'Insufficient income verification',
      };

      dataSource.transaction.mockImplementation(async (callback) => {
        const manager = {
          findOne: jest.fn().mockResolvedValue(mockApplication),
          create: jest.fn().mockReturnValue(deniedDecision),
          save: jest.fn().mockResolvedValue(deniedDecision),
        };
        return callback(manager);
      });

      const response = await request(app.getHttpServer())
        .post('/api/v1/agents/applications/app-123/decision')
        .send(denialDto)
        .expect(201);

      expect(response.body.status).toBe(DecisionStatus.DENIED);
      expect(response.body.notes).toBe('Insufficient income verification');
    });

    it('should handle database transaction failures', async () => {
      dataSource.transaction.mockRejectedValue(new Error('Database error'));

      await request(app.getHttpServer())
        .post('/api/v1/agents/applications/app-123/decision')
        .send(validDecisionDto)
        .expect(500);
    });
  });
});
