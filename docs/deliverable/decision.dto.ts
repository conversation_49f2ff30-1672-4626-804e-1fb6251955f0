import { IsEnum, IsOptional, IsString, MaxLength } from 'class-validator';

export enum DecisionStatus {
  APPROVED = 'approved',
  DENIED = 'denied',
}

export class CreateDecisionDto {
  @IsEnum(DecisionStatus)
  status: DecisionStatus;

  @IsOptional()
  @IsString()
  @MaxLength(500)
  notes?: string;
}

export class DecisionResponseDto {
  id: string;
  applicationId: string;
  status: DecisionStatus;
  notes?: string;
  decidedAt: Date;
  application: {
    id: string;
    tenantName: string;
    propertyAddress: string;
  };
}
