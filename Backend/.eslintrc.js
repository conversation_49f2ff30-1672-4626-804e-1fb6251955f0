module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: 'tsconfig.json',
    tsconfigRootDir: __dirname,
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint/eslint-plugin'],
  extends: [
    'plugin:@typescript-eslint/recommended',
    'plugin:prettier/recommended',
  ],
  root: true,
  env: {
    node: true,
    jest: true,
  },
  ignorePatterns: [
    '.eslintrc.js',
    '**/*.spec.ts',
    '**/*.test.ts',
    'dist/',
    'node_modules/',
    'documentation-controllers.ts',
    'swagger-mock-controllers.ts',
    'test-server.ts',
    'swagger-demo-server.ts'
  ],
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'off', // Turn off for now to prevent CI failures
    '@typescript-eslint/no-unused-vars': 'off', // Turn off for now to prevent CI failures
    '@typescript-eslint/no-var-requires': 'off', // Allow require statements
    'prettier/prettier': [
      'error',
      {
        endOfLine: 'auto',
      },
    ],
  },
};
