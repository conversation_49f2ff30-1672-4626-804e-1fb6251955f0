import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID, IsNumber, IsDateString, IsOptional, IsEnum } from 'class-validator';
import { CommissionStatus } from '../entities/commission.entity';

export class CreateCommissionDto {
  @ApiProperty({
    description: 'ID of the property associated with the commission',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty()
  @IsUUID()
  propertyId: string;

  @ApiProperty({
    description: 'Commission amount',
    example: 150000.0,
  })
  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @ApiProperty({
    description: 'Date when commission was earned (ISO 8601 format)',
    example: '2023-06-15T00:00:00.000Z',
  })
  @IsNotEmpty()
  @IsDateString()
  earnedDate: string;

  @ApiProperty({
    description: 'Commission status',
    enum: CommissionStatus,
    default: CommissionStatus.PENDING,
    required: false,
  })
  @IsOptional()
  @IsEnum(CommissionStatus)
  status?: CommissionStatus;

  @ApiProperty({
    description: 'Additional notes about the commission',
    required: false,
    example: 'Commission for Q2 property sale',
  })
  @IsOptional()
  notes?: string;
}
