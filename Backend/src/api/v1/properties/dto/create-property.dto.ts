import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsNumber,
  IsOptional,
  IsArray,
  IsBoolean,
  IsUUID,
  Min,
  Max,
  <PERSON><PERSON><PERSON>th,
  MaxLength,
  IsUrl,
  IsLatitude,
  IsLongitude,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PropertyType, PropertyStatus, FurnishingStatus } from '../entities/property.entity';

export class CreatePropertyDto {
  @ApiProperty({
    description: 'Property title',
    example: 'Beautiful 3-bedroom apartment in GRA',
    minLength: 10,
    maxLength: 200,
  })
  @IsString()
  @MinLength(10, { message: 'Title must be at least 10 characters long' })
  @MaxLength(200, { message: 'Title must not exceed 200 characters' })
  title: string;

  @ApiProperty({
    description: 'Property description',
    example:
      'Spacious 3-bedroom apartment with modern amenities, located in a serene environment...',
    minLength: 50,
    maxLength: 2000,
  })
  @IsString()
  @MinLength(50, { message: 'Description must be at least 50 characters long' })
  @MaxLength(2000, { message: 'Description must not exceed 2000 characters' })
  description: string;

  @ApiProperty({
    description: 'Property type',
    enum: PropertyType,
    example: PropertyType.APARTMENT,
  })
  @IsEnum(PropertyType, { message: 'Property type must be a valid type' })
  propertyType: PropertyType;

  @ApiProperty({
    description: 'Property status',
    enum: PropertyStatus,
    example: PropertyStatus.AVAILABLE,
    required: false,
  })
  @IsOptional()
  @IsEnum(PropertyStatus, { message: 'Property status must be a valid status' })
  status?: PropertyStatus;

  @ApiProperty({
    description: 'Annual rent price in Naira',
    example: 1200000,
    minimum: 100000,
    maximum: 50000000,
  })
  @IsNumber({}, { message: 'Price per year must be a valid number' })
  @Min(100000, { message: 'Annual rent must be at least ₦100,000' })
  @Max(50000000, { message: 'Annual rent must not exceed ₦50,000,000' })
  @Type(() => Number)
  pricePerYear: number;

  @ApiProperty({
    description: 'Security deposit amount',
    example: 200000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Security deposit must be a valid number' })
  @Min(0, { message: 'Security deposit cannot be negative' })
  @Type(() => Number)
  securityDeposit?: number;

  @ApiProperty({
    description: 'Agent commission percentage',
    example: 10,
    minimum: 0,
    maximum: 50,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Agent commission must be a valid number' })
  @Min(0, { message: 'Agent commission cannot be negative' })
  @Max(50, { message: 'Agent commission cannot exceed 50%' })
  @Type(() => Number)
  agentCommission?: number;

  @ApiProperty({
    description: 'Property location/address',
    example: 'No. 15 Aba Road, GRA Phase 2, Port Harcourt',
    minLength: 10,
    maxLength: 500,
  })
  @IsString()
  @MinLength(10, { message: 'Location must be at least 10 characters long' })
  @MaxLength(500, { message: 'Location must not exceed 500 characters' })
  location: string;

  @ApiProperty({
    description: 'City',
    example: 'Port Harcourt',
    required: false,
  })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiProperty({
    description: 'State',
    example: 'Rivers',
    required: false,
  })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiProperty({
    description: 'Country',
    example: 'Nigeria',
    required: false,
  })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiProperty({
    description: 'Number of bedrooms',
    example: 3,
    minimum: 0,
    maximum: 20,
  })
  @IsNumber({}, { message: 'Bedrooms must be a valid number' })
  @Min(0, { message: 'Bedrooms cannot be negative' })
  @Max(20, { message: 'Bedrooms cannot exceed 20' })
  @Type(() => Number)
  bedrooms: number;

  @ApiProperty({
    description: 'Number of bathrooms',
    example: 2,
    minimum: 1,
    maximum: 20,
  })
  @IsNumber({}, { message: 'Bathrooms must be a valid number' })
  @Min(1, { message: 'Property must have at least 1 bathroom' })
  @Max(20, { message: 'Bathrooms cannot exceed 20' })
  @Type(() => Number)
  bathrooms: number;

  @ApiProperty({
    description: 'Number of toilets',
    example: 3,
    minimum: 1,
    maximum: 20,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Toilets must be a valid number' })
  @Min(1, { message: 'Property must have at least 1 toilet' })
  @Max(20, { message: 'Toilets cannot exceed 20' })
  @Type(() => Number)
  toilets?: number;

  @ApiProperty({
    description: 'Property size in square meters',
    example: 120.5,
    minimum: 10,
    maximum: 10000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Size must be a valid number' })
  @Min(10, { message: 'Property size must be at least 10 sqm' })
  @Max(10000, { message: 'Property size cannot exceed 10,000 sqm' })
  @Type(() => Number)
  sizeInSqm?: number;

  @ApiProperty({
    description: 'Furnishing status',
    enum: FurnishingStatus,
    example: FurnishingStatus.SEMI_FURNISHED,
    required: false,
  })
  @IsOptional()
  @IsEnum(FurnishingStatus, { message: 'Furnishing status must be a valid status' })
  furnishingStatus?: FurnishingStatus;

  @ApiProperty({
    description: 'Property amenities',
    example: ['parking', 'generator', 'security', 'water', 'electricity'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  amenities?: string[];

  @ApiProperty({
    description: 'Property images URLs',
    example: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUrl({}, { each: true, message: 'Each image must be a valid URL' })
  images?: string[];

  @ApiProperty({
    description: 'Property video URL',
    example: 'https://example.com/video.mp4',
    required: false,
  })
  @IsOptional()
  @IsUrl({}, { message: 'Video URL must be a valid URL' })
  videoUrl?: string;

  @ApiProperty({
    description: 'Virtual tour URL',
    example: 'https://example.com/virtual-tour',
    required: false,
  })
  @IsOptional()
  @IsUrl({}, { message: 'Virtual tour URL must be a valid URL' })
  virtualTourUrl?: string;

  @ApiProperty({
    description: 'Latitude coordinate',
    example: 4.8156,
    required: false,
  })
  @IsOptional()
  @IsLatitude({ message: 'Latitude must be a valid coordinate' })
  @Type(() => Number)
  latitude?: number;

  @ApiProperty({
    description: 'Longitude coordinate',
    example: 7.0498,
    required: false,
  })
  @IsOptional()
  @IsLongitude({ message: 'Longitude must be a valid coordinate' })
  @Type(() => Number)
  longitude?: number;

  @ApiProperty({
    description: 'Whether property is featured',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isFeatured?: boolean;

  @ApiProperty({
    description: 'Property agent ID (if different from landlord)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID(4, { message: 'Agent ID must be a valid UUID' })
  agentId?: string;

  @ApiProperty({
    description: 'Additional property metadata',
    example: {
      nearbySchools: ['University of Port Harcourt'],
      transportLinks: ['Bus stop nearby'],
      utilities: ['NEPA', 'Borehole water'],
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
