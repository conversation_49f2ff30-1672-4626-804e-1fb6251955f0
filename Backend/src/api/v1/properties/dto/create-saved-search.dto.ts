import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsIn, IsObject } from 'class-validator';
import { SearchPropertiesDto } from './search-properties.dto';

export class CreateSavedSearchDto {
  @ApiProperty({
    description: 'Name for the saved search',
    example: 'My Dream Apartment Search',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Search filters to save',
    type: SearchPropertiesDto,
  })
  @IsObject()
  filters: SearchPropertiesDto;

  @ApiProperty({
    description: 'Frequency for email alerts on new matching properties',
    enum: ['daily', 'weekly', 'monthly', 'never'],
    default: 'never',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['daily', 'weekly', 'monthly', 'never'])
  alertFrequency?: 'daily' | 'weekly' | 'monthly' | 'never';
}
