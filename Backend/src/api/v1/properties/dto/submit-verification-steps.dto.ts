import { <PERSON><PERSON>num, <PERSON><PERSON>rray, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsUUID } from 'class-validator';
import { VerificationStepType } from '../entities/verification-step.entity';

/**
 * DTO for submitting a new verification step for a property.
 * This is used by landlords/agents to submit documents or a request for inspection.
 */
export class SubmitVerificationStepsDto {
  @IsEnum(VerificationStepType)
  stepType: VerificationStepType;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  files?: string[];

  @IsString()
  @IsOptional()
  comments?: string;
}
