import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Param,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { GetUser } from '@/common/decorators/user.decorator';
import { ParseUUIDPipe } from '@/common/pipes/parse-uuid.pipe';
import { User, UserRole } from '@/modules/users/entities/user.entity';
import { SavedSearchesService } from '@/api/v1/properties/services';
import { CreateSavedSearchDto } from '../dto/create-saved-search.dto';
import { UpdateSavedSearchDto } from '../dto/update-saved-search.dto';
import { SavedSearch } from '../entities/saved-search.entity'; // Assuming this entity exists

@ApiTags('Saved Searches')
@Controller('saved-searches')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class SavedSearchesController {
  private readonly logger = new Logger(SavedSearchesController.name);

  constructor(private readonly savedSearchesService: SavedSearchesService) {}

  @Post('create')
  @Roles(UserRole.TENANT, UserRole.LANDLORD, UserRole.AGENT, UserRole.ADMIN) // All authenticated users can save searches
  @ApiOperation({ summary: 'Create a new saved search' })
  @ApiResponse({
    status: 201,
    description: 'Saved search created successfully',
    type: SavedSearch,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  createSavedSearch(@Body() createDto: CreateSavedSearchDto, @GetUser() user: User) {
    this.logger.debug(`Creating saved search for user ${user.id}`);
    return this.savedSearchesService.createSavedSearch(createDto, user);
  }

  @Get('user/:userId')
  @Roles(UserRole.TENANT, UserRole.LANDLORD, UserRole.AGENT, UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all saved searches for a specific user' })
  @ApiResponse({
    status: 200,
    description: 'Saved searches retrieved successfully',
    type: [SavedSearch],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  getSavedSearchesByUserId(@Param('userId', ParseUUIDPipe) userId: string, @GetUser() user: User) {
    this.logger.debug(`Fetching saved searches for user ${userId}`);
    return this.savedSearchesService.getSavedSearchesByUserId(userId, user);
  }

  @Get(':id')
  @Roles(UserRole.TENANT, UserRole.LANDLORD, UserRole.AGENT, UserRole.ADMIN)
  @ApiOperation({ summary: 'Get a specific saved search by ID' })
  @ApiResponse({
    status: 200,
    description: 'Saved search retrieved successfully',
    type: SavedSearch,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Saved search not found' })
  getSavedSearchById(@Param('id', ParseUUIDPipe) id: string, @GetUser() user: User) {
    this.logger.debug(`Fetching saved search ${id}`);
    return this.savedSearchesService.getSavedSearchById(id, user);
  }

  @Patch(':id')
  @Roles(UserRole.TENANT, UserRole.LANDLORD, UserRole.AGENT, UserRole.ADMIN)
  @ApiOperation({ summary: 'Update a saved search' })
  @ApiResponse({
    status: 200,
    description: 'Saved search updated successfully',
    type: SavedSearch,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Saved search not found' })
  updateSavedSearch(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateSavedSearchDto,
    @GetUser() user: User,
  ) {
    this.logger.debug(`Updating saved search ${id}`);
    return this.savedSearchesService.updateSavedSearch(id, updateDto, user);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @Roles(UserRole.TENANT, UserRole.LANDLORD, UserRole.AGENT, UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete a saved search' })
  @ApiResponse({ status: 204, description: 'Saved search deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Saved search not found' })
  async deleteSavedSearch(@Param('id', ParseUUIDPipe) id: string, @GetUser() user: User) {
    this.logger.debug(`Deleting saved search ${id}`);
    await this.savedSearchesService.deleteSavedSearch(id, user);
  }

  @Patch(':id/toggle-active')
  @ApiOperation({ summary: 'Toggle active status of a saved search' })
  @ApiResponse({
    status: 200,
    description: 'Saved search active status toggled',
    type: SavedSearch,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Saved search not found' })
  toggleSavedSearchActive(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('isActive') isActive: boolean,
    @GetUser() user: User,
  ) {
    this.logger.debug(`Toggling active status for saved search ${id} to ${isActive}`);
    return this.savedSearchesService.toggleSavedSearchActive(id, isActive, user);
  }
}
