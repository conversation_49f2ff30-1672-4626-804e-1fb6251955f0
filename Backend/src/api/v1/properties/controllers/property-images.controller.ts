import {
  Controller,
  Post,
  Get,
  Delete,
  Patch,
  Param,
  Body,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
  HttpStatus,
  HttpCode,
  Logger,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { GetUser } from '@/common/decorators/user.decorator';
import { ParseUUIDPipe } from '@/common/pipes/parse-uuid.pipe';
import { User, UserRole } from '@/modules/users/entities/user.entity';
import { PropertyImagesService } from '../services/property-images.service';
import { UploadImageDto } from '../dto/upload-image.dto';
import { ReorderImagesDto } from '../dto/reorder-images.dto';
import { PropertyImage } from '@/api/v1/properties/entities';

@ApiTags('Property Images')
@Controller('properties/:propertyId/images')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class PropertyImagesController {
  private readonly logger = new Logger(PropertyImagesController.name);

  constructor(private readonly propertyImagesService: PropertyImagesService) {}

  @Post('upload')
  @Roles(UserRole.LANDLORD, UserRole.AGENT, UserRole.ADMIN)
  @UseInterceptors(FilesInterceptor('files')) // 'files' is the field name for the array of files
  @ApiConsumes('multipart/form-data') // Specify content type for Swagger
  @ApiOperation({ summary: 'Upload multiple images for a property' })
  @ApiResponse({
    status: 201,
    description: 'Images uploaded successfully',
    type: [PropertyImage],
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async uploadImages(
    @Param('propertyId', ParseUUIDPipe) propertyId: string,
    @UploadedFiles() files: Express.Multer.File[],
    @Body() uploadDto: UploadImageDto, // DTO for altTexts, primaryIndex
    @GetUser() user: User,
  ) {
    this.logger.debug(`Uploading images for property ${propertyId}`);
    // Multer populates files. uploadDto will contain other fields like altTexts, isPrimaryIndex
    // Make sure to pass propertyId from param to DTO if your DTO expects it directly.
    // Here, I'm passing it as a separate argument to the service.
    return this.propertyImagesService.uploadImages(propertyId, files, uploadDto, user);
  }

  @Get('all-images')
  @ApiOperation({ summary: 'Get all images for a property' })
  @ApiResponse({ status: 200, description: 'Images retrieved successfully', type: [PropertyImage] })
  @ApiResponse({ status: 404, description: 'Property not found' })
  getImages(@Param('propertyId', ParseUUIDPipe) propertyId: string) {
    this.logger.debug(`Fetching images for property ${propertyId}`);
    return this.propertyImagesService.getImagesByPropertyId(propertyId);
  }

  @Delete(':imageId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @Roles(UserRole.LANDLORD, UserRole.AGENT, UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete a specific image from a property' })
  @ApiResponse({ status: 204, description: 'Image deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Image or Property not found' })
  async deleteImage(
    @Param('propertyId', ParseUUIDPipe) propertyId: string,
    @Param('imageId', ParseUUIDPipe) imageId: string,
    @GetUser() user: User,
  ) {
    this.logger.debug(`Deleting image ${imageId} from property ${propertyId}`);
    await this.propertyImagesService.deleteImage(propertyId, imageId, user);
  }

  @Patch('reorder')
  @Roles(UserRole.LANDLORD, UserRole.AGENT, UserRole.ADMIN)
  @ApiOperation({ summary: 'Reorder images for a property' })
  @ApiResponse({
    status: 200,
    description: 'Images reordered successfully',
    type: [PropertyImage],
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Property not found' })
  reorderImages(
    @Param('propertyId', ParseUUIDPipe) propertyId: string,
    @Body() reorderDto: ReorderImagesDto,
    @GetUser() user: User,
  ) {
    this.logger.debug(`Reordering images for property ${propertyId}`);
    return this.propertyImagesService.reorderImages(propertyId, reorderDto, user);
  }
}
