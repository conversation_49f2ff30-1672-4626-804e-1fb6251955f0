import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Property } from '../entities/property.entity';
import { User, UserRole } from '@/modules/users/entities/user.entity';
import {
  VerificationStep,
  VerificationStepType,
  VerificationStepStatus,
} from '../entities/verification-step.entity';
import {
  SubmitVerificationStepsDto,
  UpdateVerificationStatusDto,
  ScheduleInspectionDto,
  CompleteInspectionDto,
} from '@/api/v1/properties/dto';

@Injectable()
export class PropertyVerificationService {
  constructor(
    @InjectRepository(VerificationStep)
    private readonly verificationStepRepository: Repository<VerificationStep>,
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
  ) {}

  /**
   * Retrieves all verification steps for a given property.
   * @param propertyId The ID of the property.
   * @returns An array of verification steps.
   */
  async getVerificationSteps(propertyId: string): Promise<VerificationStep[]> {
    const property = await this.propertyRepository.findOne({ where: { id: propertyId } });
    if (!property) {
      throw new NotFoundException(`Property with ID ${propertyId} not found.`);
    }
    return this.verificationStepRepository.find({ where: { propertyId } });
  }

  /**
   * Submits a new verification step (e.g., document upload).
   * @param propertyId The ID of the property.
   * @param stepDto Data for the new step.
   * @param user The user submitting the step.
   * @returns The newly created verification step.
   */
  async submitVerificationStep(
    propertyId: string,
    stepDto: SubmitVerificationStepsDto,
    user: User,
  ): Promise<VerificationStep> {
    const property = await this.propertyRepository.findOne({ where: { id: propertyId } });
    if (!property) {
      throw new NotFoundException(`Property with ID ${propertyId} not found.`);
    }

    // Only the property's landlord or agent can submit verification steps
    if (
      user.role === UserRole.TENANT ||
      (property.landlordId !== user.id && property.agentId !== user.id)
    ) {
      throw new ForbiddenException(
        'You do not have permission to submit verification steps for this property.',
      );
    }

    // Check if step of this type already exists and is pending/in-review
    const existingStep = await this.verificationStepRepository.findOne({
      where: {
        propertyId,
        stepType: stepDto.stepType,
        status: VerificationStepStatus.PENDING,
      },
    });

    if (existingStep) {
      throw new BadRequestException(
        `A '${stepDto.stepType}' step is already pending or in review.`,
      );
    }

    const verificationStep = this.verificationStepRepository.create({
      propertyId,
      stepType: stepDto.stepType,
      files: stepDto.files || [],
      comments: stepDto.comments || '',
      status: VerificationStepStatus.PENDING,
    });

    return this.verificationStepRepository.save(verificationStep);
  }

  /**
   * Schedule a site inspection.
   * @param propertyId The ID of the property.
   * @param scheduleDto The inspection schedule details.
   * @param user The user (admin) scheduling the inspection.
   * @returns The newly created inspection step.
   */
  async scheduleInspection(
    propertyId: string,
    scheduleDto: ScheduleInspectionDto,
    user: User,
  ): Promise<VerificationStep> {
    if (user.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can schedule inspections.');
    }

    const property = await this.propertyRepository.findOne({ where: { id: propertyId } });
    if (!property) {
      throw new NotFoundException(`Property with ID ${propertyId} not found.`);
    }

    const inspectionStep = this.verificationStepRepository.create({
      propertyId,
      stepType: VerificationStepType.SITE_INSPECTION,
      status: VerificationStepStatus.PENDING,
      comments: `Inspection scheduled for: ${scheduleDto.scheduledDate}. Notes: ${scheduleDto.notes || 'None'}.`,
    });

    return this.verificationStepRepository.save(inspectionStep);
  }

  /**
   * Complete an inspection.
   * @param stepId The ID of the inspection step.
   * @param completeDto The completion details.
   * @param user The user (admin) completing the inspection.
   * @returns The updated inspection step.
   */
  async completeInspection(
    stepId: string,
    completeDto: CompleteInspectionDto,
    user: User,
  ): Promise<VerificationStep> {
    if (user.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can complete inspections.');
    }

    const inspectionStep = await this.verificationStepRepository.findOne({ where: { id: stepId } });
    if (!inspectionStep || inspectionStep.stepType !== VerificationStepType.SITE_INSPECTION) {
      throw new NotFoundException('Inspection step not found.');
    }

    inspectionStep.status = completeDto.status;
    inspectionStep.comments = completeDto.report || inspectionStep.comments;

    const updatedStep = await this.verificationStepRepository.save(inspectionStep);
    await this.checkAndVerifyProperty(updatedStep.propertyId);

    return updatedStep;
  }

  /**
   * Get verification progress.
   * @param propertyId The ID of the property.
   * @returns A summary of the verification progress.
   */
  async getVerificationProgress(
    propertyId: string,
  ): Promise<{ total: number; completed: number; pending: number; progress: number }> {
    const steps = await this.verificationStepRepository.find({ where: { propertyId } });
    if (steps.length === 0) {
      return { total: 0, completed: 0, pending: 0, progress: 0 };
    }

    const total = steps.length;
    const completed = steps.filter(step => step.status === VerificationStepStatus.APPROVED).length;
    const pending = steps.filter(step => step.status === VerificationStepStatus.PENDING).length;
    const progress = Math.round((completed / total) * 100);

    return { total, completed, pending, progress };
  }

  /**
   * Updates the status of a specific verification step.
   * This is typically an action for an Admin.
   * @param stepId The ID of the verification step.
   * @param statusDto The new status and comments.
   * @param user The user (admin) updating the status.
   * @returns The updated verification step.
   */
  async updateVerificationStatus(
    stepId: string,
    statusDto: UpdateVerificationStatusDto,
    user: User,
  ): Promise<VerificationStep> {
    // Only admins can update verification status
    if (user.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can update property verification status.');
    }

    const verificationStep = await this.verificationStepRepository.findOne({
      where: { id: stepId },
    });
    if (!verificationStep) {
      throw new NotFoundException(`Verification step with ID ${stepId} not found.`);
    }

    // Update status and comments
    verificationStep.status = statusDto.status;
    verificationStep.comments = statusDto.comments || verificationStep.comments;

    const updatedStep = await this.verificationStepRepository.save(verificationStep);

    // If all steps are approved, automatically verify the property
    await this.checkAndVerifyProperty(updatedStep.propertyId);

    return updatedStep;
  }

  /**
   * Helper function to check if all necessary steps are approved
   * and update the property's 'isVerified' status.
   * @param propertyId The ID of the property.
   */
  private async checkAndVerifyProperty(propertyId: string): Promise<void> {
    const steps = await this.verificationStepRepository.find({ where: { propertyId } });
    const allStepsApproved = steps.every(step => step.status === VerificationStepStatus.APPROVED);

    if (allStepsApproved) {
      const property = await this.propertyRepository.findOne({ where: { id: propertyId } });
      if (property && !property.isVerified) {
        property.isVerified = true;
        await this.propertyRepository.save(property);
      }
    }
  }
}
