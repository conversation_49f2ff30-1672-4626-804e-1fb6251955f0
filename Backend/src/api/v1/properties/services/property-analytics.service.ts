import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Property } from '@/api/v1/properties/entities';
import {
  PropertyAnalytics,
  PropertyMarketData,
  PropertyRecommendation,
} from '@/api/v1/properties/entities'; // Re-using for analytics types
import { SearchPropertiesDto } from '@/api/v1/properties/dto'; // For market data filters

@Injectable()
export class PropertyAnalyticsService {
  constructor(
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
  ) {}

  async getPropertyAnalytics(propertyId: string): Promise<PropertyAnalytics> {
    const property = await this.propertyRepository.findOne({ where: { id: propertyId } });
    if (!property) {
      throw new NotFoundException(`Property with ID ${propertyId} not found`);
    }

    // This is a simplified mock. In a real scenario, you'd fetch this from
    // dedicated analytics tables, a data warehouse, or a materialized view.
    // The `viewsCount` and `inquiriesCount` are already on the Property entity.
    const viewsCount = property.viewsCount || 0;
    const inquiriesCount = property.inquiriesCount || 0;

    return {
      propertyId: property.id,
      viewsCount: viewsCount,
      inquiriesCount: inquiriesCount,
      inquiryConversionRate: viewsCount > 0 ? inquiriesCount / viewsCount : 0,
      averageResponseTimeMs: 120000, // Mock: 2 minutes
      responseRate: 0.85, // Mock: 85%
      last7DaysViews: Math.floor(viewsCount * 0.1), // Mock
      last30DaysViews: Math.floor(viewsCount * 0.3), // Mock
      totalImages: property.images?.length || 0,
      totalAmenities: property.amenities?.length || 0,
      isFeatured: property.isFeatured,
      isVerified: property.isVerified,
    };
  }

  async getPropertyMarketData(
    location: string,
    propertyType?: string,
  ): Promise<PropertyMarketData> {
    // This would typically involve complex aggregation queries on your property data
    // or integration with an external market data provider.
    // For demonstration, I'll return mock data.
    const basePrice = 5000000; // Example base annual price
    const baseSqft = 1000; // Example base sqft

    const avgPrice =
      basePrice + location.length * 100000 + (propertyType ? propertyType.length * 50000 : 0);
    const avgSqft = baseSqft + location.length * 10;

    return {
      location: location,
      propertyType: propertyType || 'all',
      averagePrice: avgPrice,
      medianPrice: avgPrice * 0.95,
      pricePerSqft: avgPrice / avgSqft,
      totalProperties: Math.floor(Math.random() * 500) + 100,
      availableProperties: Math.floor(Math.random() * 300) + 50,
      averageDaysOnMarket: Math.floor(Math.random() * 90) + 15,
      priceTrend: Math.random() > 0.6 ? 'rising' : Math.random() > 0.3 ? 'falling' : 'stable',
      demandScore: Math.floor(Math.random() * 100),
      competitionLevel: Math.random() > 0.7 ? 'high' : Math.random() > 0.3 ? 'medium' : 'low',
    };
  }

  async getPropertyRecommendations(userId: string, limit = 10): Promise<PropertyRecommendation[]> {
    // This would involve a recommendation engine
    // (e.g., collaborative filtering, content-based filtering)
    // For demonstration, I'll return a few random properties.
    const properties = await this.propertyRepository.find({ take: limit * 2 }); // Fetch more to pick from
    const recommendations: PropertyRecommendation[] = [];

    for (let i = 0; i < limit && i < properties.length; i++) {
      const prop = properties[i];
      recommendations.push({
        ...prop,
        recommendationScore: parseFloat((Math.random() * 0.5 + 0.5).toFixed(2)), // Score between 0.5 and 1.0
        reason:
          i % 2 === 0 ? 'Similar to your viewed properties' : 'Matches your saved preferences',
        fullAddress: '',
        isAvailable: true,
      });
    }
    return recommendations;
  }
}
