import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { Property, PropertyStatus } from '@/api/v1/properties/entities';
import { User, UserRole } from '@/modules/users/entities/user.entity';
import { CreatePropertyDto, UpdatePropertyDto, SearchPropertiesDto } from '@/api/v1/properties/dto';
import { PaginationUtil, PaginationResult } from '@/common/utils/pagination.util';
import { property } from 'lodash';

@Injectable()
export class PropertiesService {
  private readonly logger = new Logger(PropertiesService.name);

  constructor(
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async create(createPropertyDto: CreatePropertyDto, user: User): Promise<Property> {
    // Only landlords and agents can create properties
    this.logger.debug(
      `Creating property for ${user.role} with ${JSON.stringify(createPropertyDto)}`,
    );
    if (![UserRole.LANDLORD, UserRole.AGENT, UserRole.ADMIN].includes(user.role)) {
      throw new ForbiddenException('Only landlords, agents, and admins can create properties');
    }

    // Calculate monthly price from annual price
    const pricePerMonth = createPropertyDto.pricePerYear / 12;

    // Validate agent if provided (only for landlords assigning agents)
    if (createPropertyDto.agentId && user.role === UserRole.LANDLORD) {
      const agent = await this.userRepository.findOne({
        where: { id: createPropertyDto.agentId, role: UserRole.AGENT },
      });
      if (!agent) {
        throw new BadRequestException('Invalid agent ID provided');
      }
    }

    // Validate that agents can only assign themselves
    if (
      user.role === UserRole.AGENT &&
      createPropertyDto.agentId &&
      createPropertyDto.agentId !== user.id
    ) {
      throw new BadRequestException('Agents can only assign themselves as the agent');
    }

    // Determine landlordId and agentId based on user role and DTO
    let landlordId: string;
    let agentId: string | undefined;

    if (user.role === UserRole.LANDLORD) {
      // Landlord creating their own property
      landlordId = user.id;
      agentId = createPropertyDto.agentId; // Optional agent assignment
    } else if (user.role === UserRole.AGENT) {
      // Agent creating a property - they become the agent
      agentId = user.id;

      // For agents, we need to determine the landlord
      // This could be handled in several ways:
      // 1. Agent creates property for a specific landlord (requires landlord relationship)
      // 2. Agent creates property and becomes both agent and landlord (if they own the property)
      // 3. Agent creates property for a landlord they represent

      // For now, let's assume agents can create properties for landlords they represent
      // This would require a landlord-agent relationship table in the future
      // For current implementation, we'll set the agent as both agent and landlord
      // This can be refined based on business requirements
      landlordId = user.id; // Agent becomes the landlord for now

      // TODO: Implement landlord-agent relationship to allow agents to create properties for specific landlords
      // This would require:
      // 1. A landlord_agent_relationships table
      // 2. Validation that the agent is authorized to create properties for the landlord
      // 3. The ability to specify which landlord the property belongs to
    } else if (user.role === UserRole.ADMIN) {
      // Admin creating property - can assign to any landlord and agent
      // For security, admin should specify which landlord owns the property
      // This could be done through a separate admin endpoint or by requiring
      // the admin to impersonate a specific landlord/agent

      // For now, let's require admin to specify agentId if they want to assign an agent
      // and set the admin as the landlord (or implement a proper admin property creation flow)
      landlordId = user.id; // Admin becomes the landlord for now
      agentId = createPropertyDto.agentId; // Optional agent assignment

      // TODO: Implement proper admin property creation flow that allows
      // assigning properties to specific landlords and agents
    }

    // Create property
    const property = this.propertyRepository.create({
      ...createPropertyDto,
      pricePerMonth,
      landlordId,
      agentId,
      city: createPropertyDto.city || 'Port Harcourt',
      state: createPropertyDto.state || 'Rivers',
      country: createPropertyDto.country || 'Nigeria',
      amenities: createPropertyDto.amenities || [],
      images: createPropertyDto.images || [],
    });

    return this.propertyRepository.save(property);
  }

  async findAll(searchDto: SearchPropertiesDto, user?: User): Promise<PaginationResult<Property>> {
    // Validate price range
    if (searchDto.minPrice && searchDto.maxPrice && searchDto.minPrice > searchDto.maxPrice) {
      throw new BadRequestException('Minimum price cannot be greater than maximum price');
    }

    // Validate bedroom range
    if (
      searchDto.minBedrooms &&
      searchDto.maxBedrooms &&
      searchDto.minBedrooms > searchDto.maxBedrooms
    ) {
      throw new BadRequestException('Minimum bedrooms cannot be greater than maximum bedrooms');
    }

    // Validate bathroom range
    if (
      searchDto.minBathrooms &&
      searchDto.maxBathrooms &&
      searchDto.minBathrooms > searchDto.maxBathrooms
    ) {
      throw new BadRequestException('Minimum bathrooms cannot be greater than maximum bathrooms');
    }

    const queryBuilder = this.createSearchQuery(searchDto, user);

    return PaginationUtil.paginate(queryBuilder, {
      page: searchDto.page || 1,
      limit: searchDto.limit || 20,
      sortBy: searchDto.sortBy || 'createdAt',
      sortOrder: searchDto.sortOrder || 'DESC',
    });
  }

  async findOne(id: string, incrementViews: boolean = true): Promise<Property> {
    const property = await this.propertyRepository.findOne({
      where: { id },
      relations: ['landlord', 'agent'],
    });

    if (!property) {
      throw new NotFoundException(`Property with ID ${id} not found`);
    }

    // Increment views count
    if (incrementViews) {
      await this.propertyRepository.increment({ id }, 'viewsCount', 1);
      property.viewsCount += 1;
    }

    return property;
  }

  async update(id: string, updatePropertyDto: UpdatePropertyDto, user: User): Promise<Property> {
    const property = await this.findOne(id, false);

    // Check permissions
    if (!this.canModifyProperty(property, user)) {
      throw new ForbiddenException('You do not have permission to update this property');
    }

    // Recalculate monthly price if annual price is updated
    if (updatePropertyDto.pricePerYear) {
      updatePropertyDto['pricePerMonth'] = updatePropertyDto.pricePerYear / 12;
    }

    // Validate agent if provided
    if (updatePropertyDto.agentId) {
      const agent = await this.userRepository.findOne({
        where: { id: updatePropertyDto.agentId, role: UserRole.AGENT },
      });
      if (!agent) {
        throw new BadRequestException('Invalid agent ID provided');
      }
    }

    Object.assign(property, updatePropertyDto);
    return this.propertyRepository.save(property);
  }

  async remove(id: string, user: User): Promise<void> {
    const property = await this.findOne(id, false);

    // Check permissions
    if (!this.canModifyProperty(property, user)) {
      throw new ForbiddenException('You do not have permission to delete this property');
    }

    // TODO: Implement soft delete
    await this.propertyRepository.remove(property);
  }

  async updateStatus(id: string, status: PropertyStatus, user: User): Promise<Property> {
    const property = await this.findOne(id, false);

    // Check permissions
    if (!this.canModifyProperty(property, user)) {
      throw new ForbiddenException('You do not have permission to update this property status');
    }

    property.status = status;
    return this.propertyRepository.save(property);
  }

  async toggleFeatured(id: string, user: User): Promise<Property> {
    // Only admins and agents can feature properties
    if (![UserRole.ADMIN, UserRole.AGENT].includes(user.role)) {
      throw new ForbiddenException('Only admins and agents can feature properties');
    }

    const property = await this.findOne(id, false);
    property.isFeatured = !property.isFeatured;
    return this.propertyRepository.save(property);
  }

  async toggleVerified(id: string, user: User): Promise<Property> {
    // Only admins can verify properties
    if (user.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can verify properties');
    }

    const property = await this.findOne(id, false);
    property.isVerified = !property.isVerified;
    return this.propertyRepository.save(property);
  }

  async incrementInquiries(id: string): Promise<void> {
    const property = await this.propertyRepository.findOne({ where: { id } });
    if (!property) {
      throw new NotFoundException(`Property with ID ${id} not found`);
    }
    await this.propertyRepository.increment({ id }, 'inquiriesCount', 1);
  }

  async getMyProperties(
    user: User,
    searchDto: SearchPropertiesDto,
  ): Promise<PaginationResult<Property>> {
    const queryBuilder = this.createSearchQuery(searchDto, user);

    // Filter by user's properties
    if (user.role === UserRole.LANDLORD) {
      queryBuilder.andWhere('property.landlordId = :userId', { userId: user.id });
    } else if (user.role === UserRole.AGENT) {
      queryBuilder.andWhere('property.agentId = :userId', { userId: user.id });
    } else {
      // For other roles, return empty result
      queryBuilder.andWhere('1 = 0');
    }

    return PaginationUtil.paginate(queryBuilder, {
      page: searchDto.page || 1,
      limit: searchDto.limit || 20,
      sortBy: searchDto.sortBy || 'createdAt',
      sortOrder: searchDto.sortOrder || 'DESC',
    });
  }

  async getPropertyStats(): Promise<any> {
    const [
      totalProperties,
      availableProperties,
      rentedProperties,
      featuredProperties,
      verifiedProperties,
    ] = await Promise.all([
      this.propertyRepository.count(),
      this.propertyRepository.count({ where: { status: PropertyStatus.AVAILABLE } }),
      this.propertyRepository.count({ where: { status: PropertyStatus.RENTED } }),
      this.propertyRepository.count({ where: { isFeatured: true } }),
      this.propertyRepository.count({ where: { isVerified: true } }),
    ]);

    return {
      totalProperties,
      availableProperties,
      rentedProperties,
      featuredProperties,
      verifiedProperties,
      occupancyRate: totalProperties > 0 ? (rentedProperties / totalProperties) * 100 : 0,
    };
  }

  private createSearchQuery(
    searchDto: SearchPropertiesDto,
    user?: User,
  ): SelectQueryBuilder<Property> {
    const queryBuilder = this.propertyRepository
      .createQueryBuilder('property')
      .leftJoinAndSelect('property.landlord', 'landlord')
      .leftJoinAndSelect('property.agent', 'agent');

    // Default filter: only show available properties unless explicitly requested
    if (!searchDto.status) {
      queryBuilder.andWhere('property.status = :defaultStatus', {
        defaultStatus: PropertyStatus.AVAILABLE,
      });
    }

    // Apply filters
    if (searchDto.propertyType) {
      queryBuilder.andWhere('property.propertyType = :propertyType', {
        propertyType: searchDto.propertyType,
      });
    }

    if (searchDto.status) {
      queryBuilder.andWhere('property.status = :status', { status: searchDto.status });
    }

    if (searchDto.minPrice) {
      queryBuilder.andWhere('property.pricePerYear >= :minPrice', { minPrice: searchDto.minPrice });
    }

    if (searchDto.maxPrice) {
      queryBuilder.andWhere('property.pricePerYear <= :maxPrice', { maxPrice: searchDto.maxPrice });
    }

    if (searchDto.minBedrooms) {
      queryBuilder.andWhere('property.bedrooms >= :minBedrooms', {
        minBedrooms: searchDto.minBedrooms,
      });
    }

    if (searchDto.maxBedrooms) {
      queryBuilder.andWhere('property.bedrooms <= :maxBedrooms', {
        maxBedrooms: searchDto.maxBedrooms,
      });
    }

    if (searchDto.minBathrooms) {
      queryBuilder.andWhere('property.bathrooms >= :minBathrooms', {
        minBathrooms: searchDto.minBathrooms,
      });
    }

    if (searchDto.maxBathrooms) {
      queryBuilder.andWhere('property.bathrooms <= :maxBathrooms', {
        maxBathrooms: searchDto.maxBathrooms,
      });
    }

    if (searchDto.location) {
      queryBuilder.andWhere('property.location ILIKE :location', {
        location: `%${searchDto.location}%`,
      });
    }

    if (searchDto.city) {
      queryBuilder.andWhere('property.city ILIKE :city', { city: `%${searchDto.city}%` });
    }

    if (searchDto.state) {
      queryBuilder.andWhere('property.state ILIKE :state', { state: `%${searchDto.state}%` });
    }

    if (searchDto.furnishingStatus) {
      queryBuilder.andWhere('property.furnishingStatus = :furnishingStatus', {
        furnishingStatus: searchDto.furnishingStatus,
      });
    }

    if (searchDto.isFeatured !== undefined) {
      queryBuilder.andWhere('property.isFeatured = :isFeatured', {
        isFeatured: searchDto.isFeatured,
      });
    }

    if (searchDto.isVerified !== undefined) {
      queryBuilder.andWhere('property.isVerified = :isVerified', {
        isVerified: searchDto.isVerified,
      });
    }

    if (searchDto.landlordId) {
      // Security: Only allow filtering by own landlordId unless admin
      if (user?.role !== UserRole.ADMIN && user?.role !== UserRole.LANDLORD) {
        throw new ForbiddenException('You can only filter by your own properties');
      }
      if (user?.role === UserRole.LANDLORD && searchDto.landlordId !== user.id) {
        throw new ForbiddenException('You can only filter by your own properties');
      }
      queryBuilder.andWhere('property.landlordId = :landlordId', {
        landlordId: searchDto.landlordId,
      });
    }

    if (searchDto.agentId) {
      // Security: Only allow filtering by own agentId unless admin
      if (user?.role !== UserRole.ADMIN && user?.role !== UserRole.AGENT) {
        throw new ForbiddenException('You can only filter by your own properties');
      }
      if (user?.role === UserRole.AGENT && searchDto.agentId !== user.id) {
        throw new ForbiddenException('You can only filter by your own properties');
      }
      queryBuilder.andWhere('property.agentId = :agentId', { agentId: searchDto.agentId });
    }

    if (searchDto.amenities) {
      const amenitiesList = searchDto.amenities.split(',').map(a => a.trim());
      queryBuilder.andWhere('property.amenities && :amenities', { amenities: amenitiesList });
    }

    if (searchDto.search) {
      // Use full-text search if available, otherwise fallback to ILIKE
      const searchTerm = searchDto.search.trim();
      if (searchTerm.length > 0) {
        queryBuilder.andWhere(
          `(
            property.title ILIKE :search 
            OR property.description ILIKE :search 
            OR property.location ILIKE :search
            OR property.city ILIKE :search
            OR property.state ILIKE :search
          )`,
          { search: `%${searchTerm}%` },
        );
      }
    }

    return queryBuilder;
  }

  private canModifyProperty(property: Property, user: User): boolean {
    // Admin can modify any property
    if (user.role === UserRole.ADMIN) {
      return true;
    }

    // Landlord can modify their own properties
    if (user.role === UserRole.LANDLORD && property.landlordId === user.id) {
      return true;
    }

    // Agent can modify properties they manage
    if (user.role === UserRole.AGENT && property.agentId === user.id) {
      return true;
    }

    return false;
  }
}
