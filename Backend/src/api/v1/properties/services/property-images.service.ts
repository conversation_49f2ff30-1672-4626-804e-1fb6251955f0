import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PropertyImage } from '@/api/v1/properties/entities';
import { Property } from '../entities/property.entity';
import { User, UserRole } from '@/modules/users/entities/user.entity';
import { UploadImageDto } from '@/api/v1/properties/dto';
import { ReorderImagesDto } from '@/api/v1/properties/dto';
import { FilesService } from '@/api/v1/files/files.service';

@Injectable()
export class PropertyImagesService {
  constructor(
    @InjectRepository(PropertyImage)
    private readonly propertyImageRepository: Repository<PropertyImage>,
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
    private readonly filesService: FilesService,
  ) {}

  // Helper to check if user can manage property images
  private async canManagePropertyImages(propertyId: string, user: User): Promise<Property> {
    const property = await this.propertyRepository.findOne({ where: { id: propertyId } });
    if (!property) {
      throw new NotFoundException(`Property with ID ${propertyId} not found`);
    }

    // Admins can manage any property's images
    if (user.role === UserRole.ADMIN) {
      return property;
    }
    // Landlords can manage their own property's images
    if (user.role === UserRole.LANDLORD && property.landlordId === user.id) {
      return property;
    }
    // Agents can manage properties they are assigned to
    if (user.role === UserRole.AGENT && property.agentId === user.id) {
      return property;
    }

    throw new ForbiddenException('You do not have permission to manage images for this property');
  }

  async uploadImages(
    propertyId: string,
    files: Express.Multer.File[],
    uploadDto: UploadImageDto,
    user: User,
  ): Promise<PropertyImage[]> {
    const property = await this.canManagePropertyImages(propertyId, user);

    if (!files || files.length === 0) {
      throw new BadRequestException('No files provided for upload');
    }

    if (files.length > 10) {
      throw new BadRequestException('Maximum 10 images allowed per upload');
    }

    const uploadedImages: PropertyImage[] = [];
    const currentMaxOrder =
      (await this.propertyImageRepository.maximum('orderIndex', { propertyId })) || 0;

    // Upload all images using the FilesService
    const uploadedFiles = await this.filesService.uploadMultipleImages(files, user.id);

    for (let i = 0; i < uploadedFiles.length; i++) {
      const uploadedFile = uploadedFiles[i];
      const altText = uploadDto.altTexts?.[i] || uploadedFile.originalName;
      const isPrimary = uploadDto.isPrimaryIndex === i;

      // Calculate aspect ratio
      const aspectRatio =
        uploadedFile.width && uploadedFile.height ? uploadedFile.width / uploadedFile.height : 0;

      // Extract dominant color (simplified - in production, use a color extraction library)
      const dominantColor = '#000000'; // Placeholder - implement color extraction

      // Generate variant URLs
      const baseUrl = uploadedFile.url.replace(uploadedFile.filename, '');
      const mediumUrl = `${baseUrl}${uploadedFile.filename.replace('.', '_medium.')}`;
      const smallUrl = `${baseUrl}${uploadedFile.filename.replace('.', '_small.')}`;

      const newImage = this.propertyImageRepository.create({
        propertyId: property.id,
        url: uploadedFile.url,
        thumbnailUrl: uploadedFile.thumbnailUrl,
        mediumUrl: mediumUrl,
        smallUrl: smallUrl,
        altText: altText,
        orderIndex: currentMaxOrder + 1 + i,
        isPrimary: isPrimary,
        fileSize: uploadedFile.size,
        fileType: uploadedFile.mimetype,
        width: uploadedFile.width || 0,
        height: uploadedFile.height || 0,
        aspectRatio: aspectRatio,
        dominantColor: dominantColor,
      });

      const savedImage = await this.propertyImageRepository.save(newImage);
      uploadedImages.push(savedImage);

      // If this is the primary image, update the property's main image reference
      if (isPrimary) {
        property.images = [
          uploadedFile.url,
          ...property.images.filter(img => img !== uploadedFile.url),
        ];
      } else {
        property.images.push(uploadedFile.url);
      }
    }

    await this.propertyRepository.save(property);

    return uploadedImages;
  }

  async getImagesByPropertyId(propertyId: string): Promise<PropertyImage[]> {
    return this.propertyImageRepository.find({
      where: { propertyId },
      order: { orderIndex: 'ASC' },
    });
  }

  async deleteImage(propertyId: string, imageId: string, user: User): Promise<void> {
    const property = await this.canManagePropertyImages(propertyId, user);

    const image = await this.propertyImageRepository.findOne({
      where: { id: imageId, propertyId },
    });

    if (!image) {
      throw new NotFoundException(`Image with ID ${imageId} not found for property ${propertyId}`);
    }

    // Extract filename from URL for deletion
    const urlParts = image.url.split('/');
    const filename = urlParts[urlParts.length - 1];

    // Delete the file from storage
    try {
      await this.filesService.deleteFile('images', user.id, filename);
    } catch (error) {
      console.warn('Failed to delete file from storage:', error);
      // Continue with database deletion even if file deletion fails
    }

    // Remove from database
    await this.propertyImageRepository.remove(image);

    // Update property's images array
    property.images = property.images.filter(url => url !== image.url);
    await this.propertyRepository.save(property);
  }

  async reorderImages(
    propertyId: string,
    reorderDto: ReorderImagesDto,
    user: User,
  ): Promise<PropertyImage[]> {
    const property = await this.canManagePropertyImages(propertyId, user);

    const existingImages = await this.propertyImageRepository.find({
      where: { propertyId },
    });

    if (existingImages.length !== reorderDto.imageIds.length) {
      throw new BadRequestException(
        'Provided image IDs do not match all existing images for the property.',
      );
    }

    const updatedImages: PropertyImage[] = [];
    for (let i = 0; i < reorderDto.imageIds.length; i++) {
      const imageId = reorderDto.imageIds[i];
      const image = existingImages.find(img => img.id === imageId);

      if (!image) {
        throw new NotFoundException(
          `Image with ID ${imageId} not found for property ${propertyId}`,
        );
      }
      image.orderIndex = i;
      updatedImages.push(await this.propertyImageRepository.save(image));
    }

    // Update property's images array based on new order
    property.images = updatedImages.sort((a, b) => a.orderIndex - b.orderIndex).map(img => img.url);
    await this.propertyRepository.save(property);

    return updatedImages;
  }
}
