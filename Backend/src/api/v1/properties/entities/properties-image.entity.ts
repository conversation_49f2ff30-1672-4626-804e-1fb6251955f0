import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, Join<PERSON>olumn } from 'typeorm';
import { Property } from './property.entity';

@Entity('property_images')
export class PropertyImage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  propertyId: string;

  @Column()
  url: string;

  @Column({ nullable: true })
  thumbnailUrl: string;

  @Column({ nullable: true })
  mediumUrl: string;

  @Column({ nullable: true })
  smallUrl: string;

  @Column({ nullable: true })
  altText: string;

  @Column({ type: 'int' })
  orderIndex: number;

  @Column({ default: false })
  isPrimary: boolean;

  @Column({ nullable: true })
  fileSize: number; // in bytes

  @Column({ nullable: true })
  fileType: string;

  @Column({ nullable: true })
  width: number;

  @Column({ nullable: true })
  height: number;

  @Column({ nullable: true })
  aspectRatio: number; // width/height

  @Column({ nullable: true })
  dominantColor: string; // hex color

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @ManyToOne(() => Property, property => property.images, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'propertyId' })
  property: Property;
}
