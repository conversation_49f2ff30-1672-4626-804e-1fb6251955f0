import { <PERSON>tity, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { User } from '@/modules/users/entities/user.entity';

@Entity('saved_searches')
export class SavedSearch {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  userId: string;

  @Column()
  name: string;

  @Column({ type: 'jsonb', nullable: true })
  filters: Record<string, any>; // Store the search filters as JSON

  @Column({ default: 'never' })
  alertFrequency: 'daily' | 'weekly' | 'monthly' | 'never';

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'int', default: 0 })
  resultsCount: number; // Number of properties matching the search at last check

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @ManyToOne(() => User, user => user.savedSearches, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;
}
