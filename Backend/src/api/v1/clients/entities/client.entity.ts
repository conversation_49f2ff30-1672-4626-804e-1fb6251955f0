import { Entity, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { User } from '../../../../modules/users/entities/user.entity';

@Entity()
export class Client {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column()
  email: string;

  @Column()
  phone: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'int', default: 50 })
  leadScore: number;

  @Column({ type: 'varchar', default: 'active' })
  status: string;

  // Relations
  @ManyToOne(() => User, user => user.clients)
  agent: User;
}
