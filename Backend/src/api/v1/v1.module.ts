import { Modu<PERSON> } from '@nestjs/common';
import { AuthModule } from '../../modules/auth/auth.module';
import { UsersModule } from '../../modules/users/users.module';
import { HealthModule } from '../../modules/health/health.module';

// Import feature modules
import { PropertiesModule } from './properties/properties.module';
import { AgentsModule } from './agents/agents.module';
import { PaymentsModule } from './payments/payments.module';
import { AnalyticsModule } from './analytics/analytics.module';
import { AdminModule } from './admin/admin.module';
import { FilesModule } from './files/files.module';
import { NotificationsModule } from './notifications/notifications.module';

@Module({
  imports: [
    // Core modules
    AuthModule,
    UsersModule,
    HealthModule,

    // Feature modules
    PropertiesModule,
    AgentsModule,
    PaymentsModule,
    AnalyticsModule,
    AdminModule,
    FilesModule,
    NotificationsModule,
  ],
})
export class V1Module {}
