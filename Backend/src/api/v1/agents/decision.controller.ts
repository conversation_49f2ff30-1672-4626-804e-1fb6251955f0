import { Controller, Post, Param, Body, UseGuards, Request } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { DecisionService } from './decision.service';
import { CreateDecisionDto, DecisionResponseDto } from './dto/decision.dto';

@Controller('api/v1/agents/applications')
@UseGuards(JwtAuthGuard)
export class DecisionController {
  constructor(private readonly decisionService: DecisionService) {}

  @Post(':id/decision')
  async createDecision(
    @Param('id') applicationId: string,
    @Body() createDecisionDto: CreateDecisionDto,
    @Request() req: any,
  ): Promise<DecisionResponseDto> {
    const agentId = req.user.id;
    return this.decisionService.processDecision(
      applicationId,
      agentId,
      createDecisionDto,
    );
  }
}
