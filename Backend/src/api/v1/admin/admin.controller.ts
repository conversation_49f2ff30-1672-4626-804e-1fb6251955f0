import {
  Controller,
  Get,
  Post,
  Patch,
  Param,
  Body,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { AdminService } from './admin.service';
import { User, UserRole } from '../../../modules/users/entities/user.entity';
import { Property } from '../properties/entities/property.entity';
import { Payment } from '../payments/entities/payment.entity';
import { PaginationDto } from '../../../common/dto/pagination.dto';

import { JwtAuthGuard } from '../../../modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../modules/auth/guards/roles.guard';
import { Roles } from '../../../modules/auth/decorators/roles.decorator';
import { ParseUUIDPipe } from '../../../common/pipes/parse-uuid.pipe';
import { ApiPaginatedResponse } from '../../../common/decorators/api-paginated-response.decorator';

@ApiTags('Admin')
@Controller('admin')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
@ApiBearerAuth('JWT-auth')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Get('overview')
  @ApiOperation({ summary: 'Get system overview and statistics' })
  @ApiResponse({
    status: 200,
    description: 'System overview retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        users: {
          type: 'object',
          properties: {
            total: { type: 'number', example: 1500 },
            active: { type: 'number', example: 1200 },
            suspended: { type: 'number', example: 50 },
          },
        },
        properties: {
          type: 'object',
          properties: {
            total: { type: 'number', example: 500 },
            verified: { type: 'number', example: 400 },
            featured: { type: 'number', example: 50 },
          },
        },
        payments: {
          type: 'object',
          properties: {
            total: { type: 'number', example: 200 },
            completed: { type: 'number', example: 150 },
            totalAmount: { type: 'number', example: 50000000 },
          },
        },
      },
    },
  })
  getSystemOverview() {
    return this.adminService.getSystemOverview();
  }

  @Get('users')
  @ApiOperation({ summary: 'Get all users with pagination' })
  @ApiPaginatedResponse(User)
  @ApiResponse({
    status: 200,
    description: 'Users retrieved successfully',
  })
  getAllUsers(@Query() paginationDto: PaginationDto) {
    return this.adminService.getAllUsers(paginationDto);
  }

  @Get('properties')
  @ApiOperation({ summary: 'Get all properties with pagination' })
  @ApiPaginatedResponse(Property)
  @ApiResponse({
    status: 200,
    description: 'Properties retrieved successfully',
  })
  getAllProperties(@Query() paginationDto: PaginationDto) {
    return this.adminService.getAllProperties(paginationDto);
  }

  @Get('payments')
  @ApiOperation({ summary: 'Get all payments with pagination' })
  @ApiPaginatedResponse(Payment)
  @ApiResponse({
    status: 200,
    description: 'Payments retrieved successfully',
  })
  getAllPayments(@Query() paginationDto: PaginationDto) {
    return this.adminService.getAllPayments(paginationDto);
  }

  @Get('pending-approvals')
  @ApiOperation({ summary: 'Get items pending approval' })
  @ApiResponse({
    status: 200,
    description: 'Pending approvals retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        pendingProperties: { type: 'array', items: { $ref: '#/components/schemas/Property' } },
        pendingPayments: { type: 'array', items: { $ref: '#/components/schemas/Payment' } },
        suspendedUsers: { type: 'array', items: { $ref: '#/components/schemas/User' } },
      },
    },
  })
  getPendingApprovals() {
    return this.adminService.getPendingApprovals();
  }

  @Get('recent-activity')
  @ApiOperation({ summary: 'Get recent system activity' })
  @ApiResponse({
    status: 200,
    description: 'Recent activity retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          type: { type: 'string', example: 'user_registration' },
          description: { type: 'string', example: 'New tenant registered: John Doe' },
          timestamp: { type: 'string', example: '2023-12-01T10:00:00Z' },
          data: { type: 'object' },
        },
      },
    },
  })
  getRecentActivity() {
    return this.adminService.getRecentActivity();
  }

  @Patch('users/:id/suspend')
  @ApiOperation({ summary: 'Suspend a user' })
  @ApiResponse({
    status: 200,
    description: 'User suspended successfully',
    type: User,
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  suspendUser(@Param('id', ParseUUIDPipe) id: string) {
    return this.adminService.suspendUser(id);
  }

  @Patch('users/:id/activate')
  @ApiOperation({ summary: 'Activate a user' })
  @ApiResponse({
    status: 200,
    description: 'User activated successfully',
    type: User,
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  activateUser(@Param('id', ParseUUIDPipe) id: string) {
    return this.adminService.activateUser(id);
  }

  @Patch('properties/:id/verify')
  @ApiOperation({ summary: 'Verify a property' })
  @ApiResponse({
    status: 200,
    description: 'Property verified successfully',
    type: Property,
  })
  @ApiResponse({
    status: 404,
    description: 'Property not found',
  })
  verifyProperty(@Param('id', ParseUUIDPipe) id: string) {
    return this.adminService.verifyProperty(id);
  }

  @Patch('properties/:id/unverify')
  @ApiOperation({ summary: 'Unverify a property' })
  @ApiResponse({
    status: 200,
    description: 'Property unverified successfully',
    type: Property,
  })
  @ApiResponse({
    status: 404,
    description: 'Property not found',
  })
  unverifyProperty(@Param('id', ParseUUIDPipe) id: string) {
    return this.adminService.unverifyProperty(id);
  }

  @Patch('properties/:id/feature')
  @ApiOperation({ summary: 'Feature a property' })
  @ApiResponse({
    status: 200,
    description: 'Property featured successfully',
    type: Property,
  })
  @ApiResponse({
    status: 404,
    description: 'Property not found',
  })
  featureProperty(@Param('id', ParseUUIDPipe) id: string) {
    return this.adminService.featureProperty(id);
  }

  @Patch('properties/:id/unfeature')
  @ApiOperation({ summary: 'Unfeature a property' })
  @ApiResponse({
    status: 200,
    description: 'Property unfeatured successfully',
    type: Property,
  })
  @ApiResponse({
    status: 404,
    description: 'Property not found',
  })
  unfeatureProperty(@Param('id', ParseUUIDPipe) id: string) {
    return this.adminService.unfeatureProperty(id);
  }

  @Patch('payments/:id/approve')
  @ApiOperation({ summary: 'Approve a payment' })
  @ApiResponse({
    status: 200,
    description: 'Payment approved successfully',
    type: Payment,
  })
  @ApiResponse({
    status: 404,
    description: 'Payment not found',
  })
  approvePayment(@Param('id', ParseUUIDPipe) id: string) {
    return this.adminService.approvePayment(id);
  }

  @Patch('payments/:id/reject')
  @ApiOperation({ summary: 'Reject a payment' })
  @ApiResponse({
    status: 200,
    description: 'Payment rejected successfully',
    type: Payment,
  })
  @ApiResponse({
    status: 404,
    description: 'Payment not found',
  })
  rejectPayment(@Param('id', ParseUUIDPipe) id: string, @Body('reason') reason: string) {
    return this.adminService.rejectPayment(id, reason);
  }

  @Post('bulk-actions')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Perform bulk actions on multiple items' })
  @ApiResponse({
    status: 200,
    description: 'Bulk action completed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        affectedCount: { type: 'number', example: 5 },
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: 'Invalid bulk action',
  })
  bulkAction(@Body('action') action: string, @Body('ids') ids: string[], @Body('data') data?: any) {
    return this.adminService.bulkAction(action, ids, data);
  }
}
