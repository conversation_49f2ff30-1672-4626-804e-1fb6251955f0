import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as path from 'path';
import * as fs from 'fs/promises';
import * as sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';

export interface UploadedFile {
  id: string;
  originalName: string;
  filename: string;
  mimetype: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  uploadedAt: Date;
  width?: number;
  height?: number;
}

@Injectable()
export class FilesService {
  private readonly uploadPath: string;
  private readonly maxFileSize: number;
  private readonly allowedImageTypes: string[];
  private readonly allowedDocumentTypes: string[];

  constructor(private readonly configService: ConfigService) {
    this.uploadPath = this.configService.get('files.uploadPath') || './uploads';
    this.maxFileSize = this.configService.get('files.maxFileSize') || 10 * 1024 * 1024; // 10MB
    this.allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    this.allowedDocumentTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
    ];
  }

  async uploadImage(file: Express.Multer.File, userId: string): Promise<UploadedFile> {
    this.validateImageFile(file);

    const fileId = uuidv4();
    const fileExtension = path.extname(file.originalname);
    const filename = `${fileId}${fileExtension}`;
    const thumbnailFilename = `${fileId}_thumb${fileExtension}`;
    const mediumFilename = `${fileId}_medium${fileExtension}`;
    const smallFilename = `${fileId}_small${fileExtension}`;

    // Create upload directories
    const userUploadPath = path.join(this.uploadPath, 'images', userId);
    await this.ensureDirectoryExists(userUploadPath);

    const filePath = path.join(userUploadPath, filename);
    const thumbnailPath = path.join(userUploadPath, thumbnailFilename);
    const mediumPath = path.join(userUploadPath, mediumFilename);
    const smallPath = path.join(userUploadPath, smallFilename);

    try {
      // Get image metadata first
      const metadata = await sharp(file.buffer).metadata();

      // Process and save original image (max 1920x1080, high quality)
      await sharp(file.buffer)
        .resize(1920, 1080, { fit: 'inside', withoutEnlargement: true })
        .jpeg({ quality: 90, progressive: true, mozjpeg: true })
        .toFile(filePath);

      // Create medium size (800x600)
      await sharp(file.buffer)
        .resize(800, 600, { fit: 'inside', withoutEnlargement: true })
        .jpeg({ quality: 85, progressive: true })
        .toFile(mediumPath);

      // Create small size (400x300)
      await sharp(file.buffer)
        .resize(400, 300, { fit: 'inside', withoutEnlargement: true })
        .jpeg({ quality: 80, progressive: true })
        .toFile(smallPath);

      // Create thumbnail (300x200, cover fit for consistent thumbnails)
      await sharp(file.buffer)
        .resize(300, 200, { fit: 'cover', position: 'center' })
        .jpeg({ quality: 80, progressive: true })
        .toFile(thumbnailPath);

      const baseUrl = this.configService.get('app.baseUrl') || 'http://localhost:3001';

      return {
        id: fileId,
        originalName: file.originalname,
        filename,
        mimetype: file.mimetype,
        size: file.size,
        url: `${baseUrl}/api/v1/files/images/${userId}/${filename}`,
        thumbnailUrl: `${baseUrl}/api/v1/files/images/${userId}/${thumbnailFilename}`,
        uploadedAt: new Date(),
        width: metadata.width || 0,
        height: metadata.height || 0,
      };
    } catch (error) {
      throw new BadRequestException('Failed to process image');
    }
  }

  async uploadDocument(file: Express.Multer.File, userId: string): Promise<UploadedFile> {
    this.validateDocumentFile(file);

    const fileId = uuidv4();
    const fileExtension = path.extname(file.originalname);
    const filename = `${fileId}${fileExtension}`;

    // Create upload directories
    const userUploadPath = path.join(this.uploadPath, 'documents', userId);
    await this.ensureDirectoryExists(userUploadPath);

    const filePath = path.join(userUploadPath, filename);

    try {
      await fs.writeFile(filePath, file.buffer);

      const baseUrl = this.configService.get('app.baseUrl') || 'http://localhost:3001';

      return {
        id: fileId,
        originalName: file.originalname,
        filename,
        mimetype: file.mimetype,
        size: file.size,
        url: `${baseUrl}/api/v1/files/documents/${userId}/${filename}`,
        uploadedAt: new Date(),
      };
    } catch (error) {
      throw new BadRequestException('Failed to save document');
    }
  }

  async uploadMultipleImages(
    files: Express.Multer.File[],
    userId: string,
  ): Promise<UploadedFile[]> {
    if (files.length > 10) {
      throw new BadRequestException('Maximum 10 images allowed per upload');
    }

    const uploadPromises = files.map(file => this.uploadImage(file, userId));
    return Promise.all(uploadPromises);
  }

  async getFile(type: 'images' | 'documents', userId: string, filename: string): Promise<Buffer> {
    const filePath = path.join(this.uploadPath, type, userId, filename);

    try {
      await fs.access(filePath);
      return fs.readFile(filePath);
    } catch (error) {
      throw new NotFoundException('File not found');
    }
  }

  async deleteFile(type: 'images' | 'documents', userId: string, filename: string): Promise<void> {
    const filePath = path.join(this.uploadPath, type, userId, filename);

    try {
      await fs.unlink(filePath);

      // If it's an image, also delete the thumbnail
      if (type === 'images') {
        const fileExtension = path.extname(filename);
        const baseName = path.basename(filename, fileExtension);
        const thumbnailPath = path.join(
          this.uploadPath,
          type,
          userId,
          `${baseName}_thumb${fileExtension}`,
        );

        try {
          await fs.unlink(thumbnailPath);
        } catch (error) {
          // Thumbnail might not exist, ignore error
        }
      }
    } catch (error) {
      throw new NotFoundException('File not found');
    }
  }

  async getUserFiles(userId: string, type?: 'images' | 'documents'): Promise<string[]> {
    const files: string[] = [];

    if (!type || type === 'images') {
      const imagesPath = path.join(this.uploadPath, 'images', userId);
      try {
        const imageFiles = await fs.readdir(imagesPath);
        files.push(...imageFiles.filter(file => !file.includes('_thumb')));
      } catch (error) {
        // Directory might not exist
      }
    }

    if (!type || type === 'documents') {
      const documentsPath = path.join(this.uploadPath, 'documents', userId);
      try {
        const documentFiles = await fs.readdir(documentsPath);
        files.push(...documentFiles);
      } catch (error) {
        // Directory might not exist
      }
    }

    return files;
  }

  async getFileStats(userId: string): Promise<any> {
    const [imageFiles, documentFiles] = await Promise.all([
      this.getUserFiles(userId, 'images'),
      this.getUserFiles(userId, 'documents'),
    ]);

    let totalSize = 0;
    const imagesPath = path.join(this.uploadPath, 'images', userId);
    const documentsPath = path.join(this.uploadPath, 'documents', userId);

    // Calculate total size for images
    for (const file of imageFiles) {
      try {
        const stats = await fs.stat(path.join(imagesPath, file));
        totalSize += stats.size;
      } catch (error) {
        // File might not exist
      }
    }

    // Calculate total size for documents
    for (const file of documentFiles) {
      try {
        const stats = await fs.stat(path.join(documentsPath, file));
        totalSize += stats.size;
      } catch (error) {
        // File might not exist
      }
    }

    return {
      totalFiles: imageFiles.length + documentFiles.length,
      imageFiles: imageFiles.length,
      documentFiles: documentFiles.length,
      totalSize,
      totalSizeMB: Math.round((totalSize / (1024 * 1024)) * 100) / 100,
    };
  }

  private validateImageFile(file: Express.Multer.File): void {
    if (!this.allowedImageTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        `Invalid file type. Allowed types: ${this.allowedImageTypes.join(', ')}`,
      );
    }

    if (file.size > this.maxFileSize) {
      throw new BadRequestException(
        `File too large. Maximum size: ${this.maxFileSize / (1024 * 1024)}MB`,
      );
    }
  }

  private validateDocumentFile(file: Express.Multer.File): void {
    if (!this.allowedDocumentTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        `Invalid file type. Allowed types: ${this.allowedDocumentTypes.join(', ')}`,
      );
    }

    if (file.size > this.maxFileSize) {
      throw new BadRequestException(
        `File too large. Maximum size: ${this.maxFileSize / (1024 * 1024)}MB`,
      );
    }
  }

  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch (error) {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  private async optimizeImage(
    inputBuffer: Buffer,
    options: {
      width?: number;
      height?: number;
      quality?: number;
      format?: 'jpeg' | 'webp' | 'png';
      fit?: 'cover' | 'inside' | 'fill';
    } = {},
  ): Promise<{ buffer: Buffer; metadata: any }> {
    const { width, height, quality = 85, format = 'jpeg', fit = 'inside' } = options;

    let sharpInstance = sharp(inputBuffer);

    // Resize if dimensions provided
    if (width || height) {
      sharpInstance = sharpInstance.resize(width, height, {
        fit,
        withoutEnlargement: true,
        position: 'center',
      });
    }

    // Apply format-specific optimizations
    switch (format) {
      case 'webp':
        sharpInstance = sharpInstance.webp({
          quality,
          effort: 6,
          nearLossless: true,
        });
        break;
      case 'png':
        sharpInstance = sharpInstance.png({
          quality,
          compressionLevel: 9,
          progressive: true,
        });
        break;
      default: // jpeg
        sharpInstance = sharpInstance.jpeg({
          quality,
          progressive: true,
          mozjpeg: true,
          trellisQuantisation: true,
          overshootDeringing: true,
          optimizeCoding: true,
        });
    }

    const [optimizedBuffer, metadata] = await Promise.all([
      sharpInstance.toBuffer(),
      sharpInstance.metadata(),
    ]);

    return { buffer: optimizedBuffer, metadata };
  }

  public async extractDominantColor(inputBuffer: Buffer): Promise<string> {
    try {
      // Simplified color extraction - in production use a proper color extraction library
      const { data } = await sharp(inputBuffer)
        .resize(150, 150, { fit: 'cover' })
        .raw()
        .toBuffer({ resolveWithObject: true });

      // Calculate average color from the resized image
      let r = 0,
        g = 0,
        b = 0;
      const pixelCount = data.length / 3;

      for (let i = 0; i < data.length; i += 3) {
        r += data[i];
        g += data[i + 1];
        b += data[i + 2];
      }

      r = Math.round(r / pixelCount);
      g = Math.round(g / pixelCount);
      b = Math.round(b / pixelCount);

      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    } catch (error) {
      return '#000000'; // Fallback color
    }
  }
}
