import {
  Controller,
  Post,
  Get,
  Delete,
  Param,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  Res,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { Response } from 'express';

import { FilesService, UploadedFile as UploadedFileType } from './files.service';
import { User } from '../../../modules/users/entities/user.entity';

import { JwtAuthGuard } from '../../../modules/auth/guards/jwt-auth.guard';
import { GetUser } from '../../../common/decorators/user.decorator';
import { ParseUUIDPipe } from '../../../common/pipes/parse-uuid.pipe';

@ApiTags('Files')
@Controller('files')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class FilesController {
  constructor(private readonly filesService: FilesService) {}

  @Post('upload/image')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload a single image' })
  @ApiResponse({
    status: 201,
    description: 'Image uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: '123e4567-e89b-12d3-a456-************' },
        originalName: { type: 'string', example: 'property-image.jpg' },
        filename: { type: 'string', example: '123e4567-e89b-12d3-a456-************.jpg' },
        mimetype: { type: 'string', example: 'image/jpeg' },
        size: { type: 'number', example: 1024000 },
        url: {
          type: 'string',
          example: 'http://localhost:3001/api/v1/files/images/user-id/filename.jpg',
        },
        thumbnailUrl: {
          type: 'string',
          example: 'http://localhost:3001/api/v1/files/images/user-id/filename_thumb.jpg',
        },
        uploadedAt: { type: 'string', example: '2023-12-01T10:00:00Z' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid file type or size',
  })
  async uploadImage(
    @UploadedFile() file: Express.Multer.File,
    @GetUser() user: User,
  ): Promise<UploadedFileType> {
    return this.filesService.uploadImage(file, user.id);
  }

  @Post('upload/images')
  @UseInterceptors(FilesInterceptor('files', 10))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload multiple images (max 10)' })
  @ApiResponse({
    status: 201,
    description: 'Images uploaded successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', example: '123e4567-e89b-12d3-a456-************' },
          originalName: { type: 'string', example: 'property-image.jpg' },
          filename: { type: 'string', example: '123e4567-e89b-12d3-a456-************.jpg' },
          mimetype: { type: 'string', example: 'image/jpeg' },
          size: { type: 'number', example: 1024000 },
          url: {
            type: 'string',
            example: 'http://localhost:3001/api/v1/files/images/user-id/filename.jpg',
          },
          thumbnailUrl: {
            type: 'string',
            example: 'http://localhost:3001/api/v1/files/images/user-id/filename_thumb.jpg',
          },
          uploadedAt: { type: 'string', example: '2023-12-01T10:00:00Z' },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid file type, size, or too many files',
  })
  async uploadImages(
    @UploadedFiles() files: Express.Multer.File[],
    @GetUser() user: User,
  ): Promise<UploadedFileType[]> {
    return this.filesService.uploadMultipleImages(files, user.id);
  }

  @Post('upload/document')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload a document' })
  @ApiResponse({
    status: 201,
    description: 'Document uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: '123e4567-e89b-12d3-a456-************' },
        originalName: { type: 'string', example: 'contract.pdf' },
        filename: { type: 'string', example: '123e4567-e89b-12d3-a456-************.pdf' },
        mimetype: { type: 'string', example: 'application/pdf' },
        size: { type: 'number', example: 2048000 },
        url: {
          type: 'string',
          example: 'http://localhost:3001/api/v1/files/documents/user-id/filename.pdf',
        },
        uploadedAt: { type: 'string', example: '2023-12-01T10:00:00Z' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid file type or size',
  })
  async uploadDocument(
    @UploadedFile() file: Express.Multer.File,
    @GetUser() user: User,
  ): Promise<UploadedFileType> {
    return this.filesService.uploadDocument(file, user.id);
  }

  @Get('images/:userId/:filename')
  @ApiOperation({ summary: 'Get an image file' })
  @ApiResponse({
    status: 200,
    description: 'Image file retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'File not found',
  })
  async getImage(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Param('filename') filename: string,
    @Res() res: Response,
  ) {
    const file = await this.filesService.getFile('images', userId, filename);

    res.set({
      'Content-Type': 'image/jpeg',
      'Content-Length': file.length.toString(),
      'Cache-Control': 'public, max-age=31536000', // 1 year cache
    });

    res.send(file);
  }

  @Get('documents/:userId/:filename')
  @ApiOperation({ summary: 'Get a document file' })
  @ApiResponse({
    status: 200,
    description: 'Document file retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'File not found',
  })
  async getDocument(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Param('filename') filename: string,
    @Res() res: Response,
  ) {
    const file = await this.filesService.getFile('documents', userId, filename);

    res.set({
      'Content-Type': 'application/octet-stream',
      'Content-Length': file.length.toString(),
      'Content-Disposition': `attachment; filename="${filename}"`,
    });

    res.send(file);
  }

  @Get('my-files')
  @ApiOperation({ summary: 'Get current user files' })
  @ApiResponse({
    status: 200,
    description: 'User files retrieved successfully',
    schema: {
      type: 'array',
      items: { type: 'string', example: 'filename.jpg' },
    },
  })
  async getMyFiles(
    @GetUser() user: User,
    @Query('type') type?: 'images' | 'documents',
  ): Promise<string[]> {
    return this.filesService.getUserFiles(user.id, type);
  }

  @Get('my-files/stats')
  @ApiOperation({ summary: 'Get current user file statistics' })
  @ApiResponse({
    status: 200,
    description: 'User file statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalFiles: { type: 'number', example: 25 },
        imageFiles: { type: 'number', example: 20 },
        documentFiles: { type: 'number', example: 5 },
        totalSize: { type: 'number', example: 10485760 },
        totalSizeMB: { type: 'number', example: 10.5 },
      },
    },
  })
  async getMyFileStats(@GetUser() user: User) {
    return this.filesService.getFileStats(user.id);
  }

  @Delete('images/:filename')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete an image file' })
  @ApiResponse({
    status: 204,
    description: 'Image deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'File not found',
  })
  async deleteImage(@Param('filename') filename: string, @GetUser() user: User): Promise<void> {
    return this.filesService.deleteFile('images', user.id, filename);
  }

  @Delete('documents/:filename')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a document file' })
  @ApiResponse({
    status: 204,
    description: 'Document deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'File not found',
  })
  async deleteDocument(@Param('filename') filename: string, @GetUser() user: User): Promise<void> {
    return this.filesService.deleteFile('documents', user.id, filename);
  }
}
