import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TestController } from './test.controller';
import { MigrationController } from './database/migration.controller';
import { SupabaseMigrationService } from './database/supabase-migration.service';

// Import all mock controllers for Swagger documentation
import {
  MockAuthController,
  MockUsersController,
  MockPropertiesController,
  MockAgentsController,
  MockPaymentsController,
  MockAnalyticsController,
  MockAdminController,
  MockFilesController,
  MockNotificationsController,
  MockHealthController,
} from './swagger-mock-controllers';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
  ],
  controllers: [
    TestController,
    MigrationController,
    // All mock controllers for complete Swagger documentation
    MockAuthController,
    MockUsersController,
    MockPropertiesController,
    MockAgentsController,
    MockPaymentsController,
    MockAnalyticsController,
    MockAdminController,
    MockFilesController,
    MockNotificationsController,
    MockHealthController,
  ],
  providers: [
    {
      provide: SupabaseMigrationService,
      useValue: {
        getMigrationStatus: () => ({ message: 'Mock service - requires database connection' }),
        convertSupabaseMigrations: () => ({
          message: 'Mock service - requires database connection',
        }),
        runConvertedMigrations: () => ({ message: 'Mock service - requires database connection' }),
        createComprehensiveSchema: () => ({
          message: 'Mock service - requires database connection',
        }),
        generateSeedData: () => ({ message: 'Mock service - requires database connection' }),
      },
    },
  ],
})
export class TestModule {}
