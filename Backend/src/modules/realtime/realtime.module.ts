import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RealtimeGateway } from './realtime.gateway';
import { RealtimeService } from './realtime.service';
import { RealtimeController } from './realtime.controller';
import { WsJwtGuard } from './guards/ws-jwt.guard';
import { User } from '../users/entities/user.entity';
import { Property } from '@/api/v1/properties/entities/property.entity';
import { Payment } from '@/api/v1/payments/entities/payment.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Property, Payment]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: { expiresIn: '1d' },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [RealtimeController],
  providers: [RealtimeGateway, RealtimeService, WsJwtGuard],
  exports: [RealtimeGateway, RealtimeService],
})
export class RealtimeModule {}
