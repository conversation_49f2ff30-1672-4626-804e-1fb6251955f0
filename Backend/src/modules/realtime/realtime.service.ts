import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { User } from '../users/entities/user.entity';
import { Property } from '@/api/v1/properties/entities/property.entity';
import { Payment } from '@/api/v1/payments/entities/payment.entity';
import {
  RealtimeGateway,
  AgentMetrics,
  PropertyUpdate,
  PaymentAlert,
  DashboardMetrics,
} from './realtime.gateway';

@Injectable()
export class RealtimeService {
  private readonly logger = new Logger(RealtimeService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
    @InjectRepository(Payment)
    private readonly paymentRepository: Repository<Payment>,
    private readonly realtimeGateway: RealtimeGateway,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Update agent metrics and emit real-time event
   */
  async updateAgentMetrics(agentId: string): Promise<void> {
    try {
      const metrics = await this.calculateAgentMetrics(agentId);
      this.realtimeGateway.emitAgentMetricsUpdate(agentId, metrics);
      this.logger.log(`Agent metrics updated for agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Failed to update agent metrics for ${agentId}:`, error);
    }
  }

  /**
   * Update property data and emit real-time event
   */
  async updatePropertyData(propertyId: string): Promise<void> {
    try {
      const property = await this.propertyRepository.findOne({
        where: { id: propertyId },
        relations: ['landlord', 'agent'],
      });

      if (!property) {
        this.logger.warn(`Property ${propertyId} not found`);
        return;
      }

      const update: PropertyUpdate = {
        propertyId: property.id,
        status: property.isAvailable ? 'available' : 'rented',
        viewsCount: property.viewsCount || 0,
        inquiriesCount: property.inquiriesCount || 0,
        lastViewed: property.updatedAt?.toISOString() || new Date().toISOString(),
        priceChanges: [], // Placeholder - would need price history tracking
      };

      this.realtimeGateway.emitPropertyUpdate(propertyId, update);
      this.logger.log(`Property update emitted for property ${propertyId}`);
    } catch (error) {
      this.logger.error(`Failed to update property data for ${propertyId}:`, error);
    }
  }

  /**
   * Update payment status and emit real-time event
   */
  async updatePaymentStatus(paymentId: string): Promise<void> {
    try {
      const payment = await this.paymentRepository.findOne({
        where: { id: paymentId },
        relations: ['property', 'user'],
      });

      if (!payment) {
        this.logger.warn(`Payment ${paymentId} not found`);
        return;
      }

      const alert: PaymentAlert = {
        transactionId: payment.id,
        status: payment.status as any, // Type assertion for compatibility
        amount: payment.amount,
        propertyId: payment.propertyId || '',
        agentId: payment.recipientId || '', // Assuming recipient could be agent
        tenantId: payment.userId,
        paymentMethod: payment.gateway,
        timestamp: payment.updatedAt?.toISOString() || new Date().toISOString(),
      };

      this.realtimeGateway.emitPaymentStatusChange(alert);
      this.logger.log(`Payment status update emitted for payment ${paymentId}`);
    } catch (error) {
      this.logger.error(`Failed to update payment status for ${paymentId}:`, error);
    }
  }

  /**
   * Update dashboard metrics and emit real-time event
   */
  async updateDashboardMetrics(userId: string): Promise<void> {
    try {
      const metrics = await this.calculateDashboardMetrics(userId);
      this.realtimeGateway.emitDashboardMetricsUpdate(userId, metrics);
      this.logger.log(`Dashboard metrics updated for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to update dashboard metrics for ${userId}:`, error);
    }
  }

  /**
   * Send system notification
   */
  async sendSystemNotification(notification: {
    userId?: string;
    title: string;
    message: string;
    type: string;
    data?: any;
  }): Promise<void> {
    try {
      this.realtimeGateway.emitSystemNotification({
        ...notification,
        timestamp: new Date().toISOString(),
      });
      this.logger.log(`System notification sent: ${notification.title}`);
    } catch (error) {
      this.logger.error('Failed to send system notification:', error);
    }
  }

  /**
   * Calculate agent metrics
   */
  private async calculateAgentMetrics(agentId: string): Promise<AgentMetrics> {
    const agent = await this.userRepository.findOne({
      where: { id: agentId },
    });

    if (!agent) {
      throw new Error(`Agent ${agentId} not found`);
    }

    // Get agent's properties
    const properties = await this.propertyRepository.find({
      where: { agentId: agentId },
    });

    // Get agent's payments (as recipient)
    const payments = await this.paymentRepository.find({
      where: { recipientId: agentId },
    });

    const totalProperties = properties.length;
    const activeProperties = properties.filter(p => p.isAvailable).length;
    const monthlyEarnings = payments
      .filter(
        p =>
          p.status === 'completed' &&
          p.createdAt >= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      )
      .reduce((sum, p) => sum + p.amount, 0);

    const conversionRate = totalProperties > 0 ? (activeProperties / totalProperties) * 100 : 0;

    return {
      agentId: agentId,
      totalProperties: totalProperties,
      activeClients: activeProperties, // Simplified
      monthlyEarnings: monthlyEarnings,
      conversionRate: conversionRate,
      responseTime: 24, // Placeholder
      clientSatisfaction: 4.5, // Placeholder
      dealsCompleted: payments.filter(p => p.status === 'completed').length,
      lastUpdated: new Date().toISOString(),
    };
  }

  /**
   * Calculate dashboard metrics
   */
  private async calculateDashboardMetrics(userId: string): Promise<DashboardMetrics> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new Error(`User ${userId} not found`);
    }

    // Get user's properties (if landlord)
    const properties = await this.propertyRepository.find({
      where: { landlordId: userId },
    });

    // Get user's payments
    const payments = await this.paymentRepository.find({
      where: { userId: userId },
    });

    const totalRevenue = payments
      .filter(p => p.status === 'completed')
      .reduce((sum, p) => sum + p.amount, 0);

    const activeProperties = properties.filter(p => p.isAvailable).length;
    const pendingPayments = payments.filter(p => p.status === 'pending').length;
    const newInquiries = 0; // Placeholder - would need inquiry entity

    const conversionRate = properties.length > 0 ? (activeProperties / properties.length) * 100 : 0;
    const growthRate = 0; // Placeholder - would need historical data

    return {
      userId: userId,
      totalRevenue: totalRevenue,
      activeProperties: activeProperties,
      pendingPayments: pendingPayments,
      newInquiries: newInquiries,
      conversionRate: conversionRate,
      growthRate: growthRate,
      lastUpdated: new Date().toISOString(),
    };
  }

  /**
   * Get connected clients count
   */
  getConnectedClientsCount(): number {
    return this.realtimeGateway.server.engine.clientsCount;
  }

  /**
   * Get server statistics
   */
  getServerStats() {
    return {
      connectedClients: this.getConnectedClientsCount(),
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      timestamp: new Date().toISOString(),
    };
  }
}
