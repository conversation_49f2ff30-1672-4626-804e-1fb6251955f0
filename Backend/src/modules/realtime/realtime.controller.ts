import { Controller, Get, Post, Body, Param, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RealtimeService } from './realtime.service';
import { RealtimeGateway } from './realtime.gateway';

@ApiTags('Real-time')
@Controller('realtime')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RealtimeController {
  constructor(
    private readonly realtimeService: RealtimeService,
    private readonly realtimeGateway: RealtimeGateway,
  ) {}

  @Get('stats')
  @ApiOperation({ summary: 'Get real-time server statistics' })
  @ApiResponse({
    status: 200,
    description: 'Server statistics retrieved successfully',
  })
  getServerStats() {
    return this.realtimeService.getServerStats();
  }

  @Get('connected-clients')
  @ApiOperation({ summary: 'Get number of connected clients' })
  @ApiResponse({
    status: 200,
    description: 'Connected clients count retrieved successfully',
  })
  getConnectedClientsCount() {
    return {
      connectedClients: this.realtimeService.getConnectedClientsCount(),
      timestamp: new Date().toISOString(),
    };
  }

  @Post('agent-metrics/:agentId/update')
  @ApiOperation({ summary: 'Trigger agent metrics update' })
  @ApiResponse({
    status: 200,
    description: 'Agent metrics update triggered successfully',
  })
  async updateAgentMetrics(@Param('agentId') agentId: string) {
    await this.realtimeService.updateAgentMetrics(agentId);
    return { message: 'Agent metrics update triggered successfully' };
  }

  @Post('property/:propertyId/update')
  @ApiOperation({ summary: 'Trigger property data update' })
  @ApiResponse({
    status: 200,
    description: 'Property update triggered successfully',
  })
  async updatePropertyData(@Param('propertyId') propertyId: string) {
    await this.realtimeService.updatePropertyData(propertyId);
    return { message: 'Property update triggered successfully' };
  }

  @Post('payment/:paymentId/update')
  @ApiOperation({ summary: 'Trigger payment status update' })
  @ApiResponse({
    status: 200,
    description: 'Payment update triggered successfully',
  })
  async updatePaymentStatus(@Param('paymentId') paymentId: string) {
    await this.realtimeService.updatePaymentStatus(paymentId);
    return { message: 'Payment update triggered successfully' };
  }

  @Post('dashboard-metrics/:userId/update')
  @ApiOperation({ summary: 'Trigger dashboard metrics update' })
  @ApiResponse({
    status: 200,
    description: 'Dashboard metrics update triggered successfully',
  })
  async updateDashboardMetrics(@Param('userId') userId: string) {
    await this.realtimeService.updateDashboardMetrics(userId);
    return { message: 'Dashboard metrics update triggered successfully' };
  }

  @Post('notifications/send')
  @ApiOperation({ summary: 'Send system notification' })
  @ApiResponse({
    status: 200,
    description: 'System notification sent successfully',
  })
  async sendSystemNotification(
    @Body()
    notification: {
      userId?: string;
      title: string;
      message: string;
      type: string;
      data?: any;
    },
  ) {
    await this.realtimeService.sendSystemNotification(notification);
    return { message: 'System notification sent successfully' };
  }

  @Post('test-connection')
  @ApiOperation({ summary: 'Test real-time connection' })
  @ApiResponse({ status: 200, description: 'Connection test completed' })
  async testConnection(@Request() req) {
    const userId = req.user.id;

    // Send a test notification to the current user
    await this.realtimeService.sendSystemNotification({
      userId,
      title: 'Connection Test',
      message: 'Real-time connection is working properly',
      type: 'test',
      data: { timestamp: new Date().toISOString() },
    });

    return {
      message: 'Test notification sent successfully',
      userId,
      timestamp: new Date().toISOString(),
    };
  }
}
