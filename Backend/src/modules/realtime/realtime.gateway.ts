import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { WsJwtGuard } from './guards/ws-jwt.guard';
import { WsUser } from './decorators/ws-user.decorator';
import { User } from '@/modules/users/entities/user.entity';

export interface RealTimeEvent {
  type: string;
  payload: any;
  timestamp: number;
  userId?: string;
}

export interface SubscriptionRequest {
  eventType: string;
  filters?: Record<string, any>;
}

export interface AgentMetrics {
  agentId: string;
  totalProperties: number;
  activeClients: number;
  monthlyEarnings: number;
  conversionRate: number;
  responseTime: number;
  clientSatisfaction: number;
  dealsCompleted: number;
  lastUpdated: string;
}

export interface PropertyUpdate {
  propertyId: string;
  status: 'available' | 'rented' | 'maintenance' | 'pending';
  viewsCount: number;
  inquiriesCount: number;
  lastViewed: string;
  priceChanges: Array<{
    oldPrice: number;
    newPrice: number;
    changedAt: string;
  }>;
}

export interface PaymentAlert {
  transactionId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  amount: number;
  propertyId: string;
  agentId: string;
  tenantId: string;
  paymentMethod: string;
  timestamp: string;
}

export interface DashboardMetrics {
  userId: string;
  totalRevenue: number;
  activeProperties: number;
  pendingPayments: number;
  newInquiries: number;
  conversionRate: number;
  growthRate: number;
  lastUpdated: string;
}

@WebSocketGateway({
  cors: {
    origin: process.env.NODE_ENV === 'production' ? process.env.FRONTEND_URL?.split(',') : '*',
    credentials: true,
  },
  namespace: '/realtime',
})
export class RealtimeGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(RealtimeGateway.name);
  private connectedClients = new Map<
    string,
    { socket: Socket; user: User; subscriptions: Set<string> }
  >();

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  afterInit(server: Server) {
    this.logger.log('Realtime Gateway initialized');

    // Set up event listeners for database changes
    this.setupDatabaseEventListeners();
  }

  async handleConnection(client: Socket) {
    try {
      const token = this.extractToken(client);
      if (!token) {
        client.disconnect();
        return;
      }

      const payload = this.jwtService.verify(token, {
        secret: this.configService.get('JWT_SECRET'),
      });

      const user = payload.user;
      this.connectedClients.set(client.id, {
        socket: client,
        user,
        subscriptions: new Set(),
      });

      this.logger.log(`Client connected: ${client.id} (User: ${user.id})`);

      // Send connection confirmation
      client.emit('connected', {
        message: 'Connected to real-time service',
        userId: user.id,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error(`Connection error: ${error.message}`);
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    const clientData = this.connectedClients.get(client.id);
    if (clientData) {
      this.logger.log(`Client disconnected: ${client.id} (User: ${clientData.user.id})`);
      this.connectedClients.delete(client.id);
    }
  }

  @UseGuards(WsJwtGuard)
  @SubscribeMessage('subscribe')
  handleSubscribe(
    @MessageBody() data: SubscriptionRequest,
    @ConnectedSocket() client: Socket,
    @WsUser() user: User,
  ) {
    const clientData = this.connectedClients.get(client.id);
    if (!clientData) {
      return { error: 'Client not authenticated' };
    }

    const subscriptionId = `${data.eventType}_${Date.now()}`;
    clientData.subscriptions.add(subscriptionId);

    // Join the appropriate room based on event type and filters
    const roomName = this.getRoomName(data.eventType, data.filters, user.id);
    client.join(roomName);

    this.logger.log(`User ${user.id} subscribed to ${data.eventType} in room ${roomName}`);

    return {
      success: true,
      subscriptionId,
      eventType: data.eventType,
      room: roomName,
    };
  }

  @UseGuards(WsJwtGuard)
  @SubscribeMessage('unsubscribe')
  handleUnsubscribe(
    @MessageBody() data: { subscriptionId: string },
    @ConnectedSocket() client: Socket,
    @WsUser() user: User,
  ) {
    const clientData = this.connectedClients.get(client.id);
    if (!clientData) {
      return { error: 'Client not authenticated' };
    }

    clientData.subscriptions.delete(data.subscriptionId);

    // Leave all rooms (simplified approach)
    client.rooms.forEach(room => {
      if (room !== client.id) {
        // Don't leave the default room
        client.leave(room);
      }
    });

    this.logger.log(`User ${user.id} unsubscribed from ${data.subscriptionId}`);

    return { success: true, subscriptionId: data.subscriptionId };
  }

  @UseGuards(WsJwtGuard)
  @SubscribeMessage('ping')
  handlePing(@ConnectedSocket() client: Socket) {
    client.emit('pong', { timestamp: Date.now() });
  }

  // Public methods to emit events to specific rooms
  emitToRoom(room: string, event: string, data: any) {
    this.server.to(room).emit(event, data);
  }

  emitToUser(userId: string, event: string, data: any) {
    this.server.to(`user:${userId}`).emit(event, data);
  }

  emitToAgent(agentId: string, event: string, data: any) {
    this.server.to(`agent:${agentId}`).emit(event, data);
  }

  emitToProperty(propertyId: string, event: string, data: any) {
    this.server.to(`property:${propertyId}`).emit(event, data);
  }

  // Database change event handlers
  private setupDatabaseEventListeners() {
    // Agent metrics updates
    this.eventEmitter.on('agent.metrics.updated', (data: AgentMetrics) => {
      this.emitToAgent(data.agentId, 'agent_metrics', data);
    });

    // Property updates
    this.eventEmitter.on('property.updated', (data: PropertyUpdate) => {
      this.emitToProperty(data.propertyId, 'property_updates', data);
    });

    // Payment alerts
    this.eventEmitter.on('payment.status.changed', (data: PaymentAlert) => {
      this.emitToUser(data.tenantId, 'payment_alerts', data);
      if (data.agentId) {
        this.emitToAgent(data.agentId, 'payment_alerts', data);
      }
    });

    // Dashboard metrics
    this.eventEmitter.on('dashboard.metrics.updated', (data: DashboardMetrics) => {
      this.emitToUser(data.userId, 'dashboard_metrics', data);
    });

    // System notifications
    this.eventEmitter.on('system.notification', (data: any) => {
      if (data.userId) {
        this.emitToUser(data.userId, 'system_notifications', data);
      } else {
        this.server.emit('system_notifications', data);
      }
    });
  }

  private extractToken(client: Socket): string | null {
    const auth =
      client.handshake.auth?.token ||
      client.handshake.headers?.authorization ||
      client.handshake.query?.token;

    if (!auth) return null;

    if (typeof auth === 'string' && auth.startsWith('Bearer ')) {
      return auth.substring(7);
    }

    return auth;
  }

  private getRoomName(
    eventType: string,
    filters: Record<string, any> = {},
    userId: string,
  ): string {
    switch (eventType) {
      case 'agent_metrics':
        return `agent:${filters.agentId || userId}`;
      case 'property_updates':
        return `property:${filters.propertyId}`;
      case 'payment_alerts':
        return `user:${userId}`;
      case 'dashboard_metrics':
        return `user:${userId}`;
      case 'system_notifications':
        return `user:${userId}`;
      default:
        return `user:${userId}`;
    }
  }

  // Utility methods for external services to emit events
  public emitAgentMetricsUpdate(agentId: string, metrics: AgentMetrics) {
    this.eventEmitter.emit('agent.metrics.updated', metrics);
  }

  public emitPropertyUpdate(propertyId: string, update: PropertyUpdate) {
    this.eventEmitter.emit('property.updated', update);
  }

  public emitPaymentStatusChange(alert: PaymentAlert) {
    this.eventEmitter.emit('payment.status.changed', alert);
  }

  public emitDashboardMetricsUpdate(userId: string, metrics: DashboardMetrics) {
    this.eventEmitter.emit('dashboard.metrics.updated', metrics);
  }

  public emitSystemNotification(notification: any) {
    this.eventEmitter.emit('system.notification', notification);
  }
}
