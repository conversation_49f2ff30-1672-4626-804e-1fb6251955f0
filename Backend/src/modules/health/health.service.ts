import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as os from 'os';

@Injectable()
export class HealthService {
  constructor(private readonly configService: ConfigService) {}

  async getHealthStatus(): Promise<any> {
    const uptime = process.uptime();
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: Math.floor(uptime),
      environment: this.configService.get('NODE_ENV') || 'development',
      version: process.version,
      platform: process.platform,
      memory: {
        used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        external: Math.round(memoryUsage.external / 1024 / 1024),
        rss: Math.round(memoryUsage.rss / 1024 / 1024),
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
    };
  }

  async getDatabaseHealth(): Promise<any> {
    // This would normally check database connectivity
    // For now, return a mock response
    return {
      status: 'ok',
      database: 'postgresql',
      connected: true,
      responseTime: Math.floor(Math.random() * 50) + 10, // Mock response time
      timestamp: new Date().toISOString(),
    };
  }

  async getMemoryHealth(): Promise<any> {
    const memoryUsage = process.memoryUsage();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;

    const memoryUsagePercent = (usedMemory / totalMemory) * 100;
    const heapUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;

    return {
      status: memoryUsagePercent > 90 ? 'critical' : memoryUsagePercent > 70 ? 'warning' : 'ok',
      system: {
        total: Math.round(totalMemory / 1024 / 1024),
        used: Math.round(usedMemory / 1024 / 1024),
        free: Math.round(freeMemory / 1024 / 1024),
        usagePercent: Math.round(memoryUsagePercent * 100) / 100,
      },
      process: {
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        external: Math.round(memoryUsage.external / 1024 / 1024),
        rss: Math.round(memoryUsage.rss / 1024 / 1024),
        heapUsagePercent: Math.round(heapUsagePercent * 100) / 100,
      },
      timestamp: new Date().toISOString(),
    };
  }

  async getDiskHealth(): Promise<any> {
    try {
      const stats = fs.statSync('.');

      return {
        status: 'ok',
        disk: {
          accessible: true,
          path: process.cwd(),
          timestamp: new Date().toISOString(),
        },
        note: 'Disk space monitoring requires additional system tools',
      };
    } catch (error) {
      return {
        status: 'error',
        disk: {
          accessible: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        },
      };
    }
  }
}
