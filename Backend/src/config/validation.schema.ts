import * as <PERSON><PERSON> from 'joi';

export default Joi.object({
  // Node environment
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test', 'staging')
    .default('development'),

  // Application
  PORT: Joi.number().default(3001),
  APP_NAME: Joi.string().default('PHCityRent API'),
  APP_VERSION: Joi.string().default('1.0.0'),
  APP_DESCRIPTION: Joi.string(),
  CORS_ORIGINS: Joi.string().default('http://localhost:3000,http://localhost:8080'),
  API_PREFIX: Joi.string().default('api/v1'),

  // Database
  DB_HOST: Joi.string().default('localhost'),
  DB_PORT: Joi.number().default(5432),
  DB_USERNAME: Joi.string().required(),
  DB_PASSWORD: Joi.string().required(),
  DB_NAME: Joi.string().required(),
  DB_MAX_CONNECTIONS: Joi.number().default(20),
  DB_MIN_CONNECTIONS: Joi.number().default(5),

  // Redis
  REDIS_HOST: Joi.string().default('localhost'),
  REDIS_PORT: Joi.number().default(6379),
  REDIS_PASSWORD: Joi.string().allow('', null),
  REDIS_DB: Joi.number().default(0),
  REDIS_QUEUE_DB: Joi.number().default(1),
  REDIS_SESSION_DB: Joi.number().default(2),

  // Authentication
  JWT_SECRET: Joi.string().required(),
  JWT_EXPIRES_IN: Joi.string().default('15m'),
  JWT_REFRESH_SECRET: Joi.string().required(),
  JWT_REFRESH_EXPIRES_IN: Joi.string().default('7d'),
  BCRYPT_ROUNDS: Joi.number().default(12),
  PASSWORD_RESET_EXPIRY: Joi.number().default(3600000), // 1 hour
  EMAIL_VERIFICATION_EXPIRY: Joi.number().default(86400000), // 24 hours
  MAX_LOGIN_ATTEMPTS: Joi.number().default(5),
  LOCKOUT_DURATION: Joi.number().default(900000), // 15 minutes

  // File uploads
  MAX_FILE_SIZE: Joi.number().default(10485760), // 10MB
  ALLOWED_IMAGE_TYPES: Joi.string().default('image/jpeg,image/png,image/webp'),
  UPLOAD_PATH: Joi.string().default('./uploads'),

  // Email
  EMAIL_FROM: Joi.string().email().default('<EMAIL>'),
  EMAIL_HOST: Joi.string().default('smtp.gmail.com'),
  EMAIL_PORT: Joi.number().default(587),
  EMAIL_USER: Joi.string().allow('', null),
  EMAIL_PASSWORD: Joi.string().allow('', null),

  // Payment gateways
  PAYSTACK_SECRET_KEY: Joi.string().allow('', null),
  PAYSTACK_PUBLIC_KEY: Joi.string().allow('', null),
  PAYSTACK_WEBHOOK_SECRET: Joi.string().allow('', null),

  FLUTTERWAVE_SECRET_KEY: Joi.string().allow('', null),
  FLUTTERWAVE_PUBLIC_KEY: Joi.string().allow('', null),
  FLUTTERWAVE_WEBHOOK_SECRET: Joi.string().allow('', null),

  // Rate limiting
  THROTTLE_TTL: Joi.number().default(60),
  THROTTLE_LIMIT: Joi.number().default(100),

  // Caching
  CACHE_TTL: Joi.number().default(300), // 5 minutes
  CACHE_MAX_ITEMS: Joi.number().default(1000),

  // Pagination
  DEFAULT_PAGE_SIZE: Joi.number().default(20),
  MAX_PAGE_SIZE: Joi.number().default(100),

  // Metrics and monitoring
  ENABLE_METRICS: Joi.boolean().default(false),
  METRICS_PORT: Joi.number().default(9090),
});
