import { MigrationInterface, QueryRunner } from 'typeorm';

export class B24f43fd481240518556Eea4575f520620250614095150 implements MigrationInterface {
  name = 'B24f43fd481240518556Eea4575f520620250614095150';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Converted from Supabase migration: B24f43fd481240518556Eea4575f5206
    await queryRunner.query(`CREATE TYPE application_status AS ENUM (
    'pending_review',
    'documents_reviewed', 
    'referee_contacted',
    'approved',
    'rejected',
    'needs_info'
)`);
    await queryRunner.query(`CREATE TYPE document_type AS ENUM (
    'id_document',
    'selfie_with_id',
    'cac_document'
)`);
    await queryRunner.query(`CREATE TYPE referee_status AS ENUM (
    'pending',
    'contacted', 
    'confirmed',
    'failed'
)`);
    await queryRunner.query(`CREATE TABLE public.agent_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id TEXT UNIQUE NOT NULL, 
    full_name TEXT NOT NULL,
    whatsapp_number TEXT NOT NULL,
    email TEXT,
    residential_address TEXT NOT NULL,
    operating_areas TEXT[] NOT NULL,
    is_registered_business BOOLEAN DEFAULT FALSE,
    status application_status DEFAULT 'pending_review',
    reviewer_notes TEXT,
    next_action TEXT,
    estimated_completion DATE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
)`);
    await queryRunner.query(`CREATE TABLE public.agent_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    application_id UUID REFERENCES public.agent_applications(id) ON DELETE CASCADE,
    agent_id TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    whatsapp_number TEXT NOT NULL,
    email TEXT,
    operating_areas TEXT[] NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    verification_date TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
)`);
    await queryRunner.query(`CREATE TABLE public.verification_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    application_id UUID REFERENCES public.agent_applications(id) ON DELETE CASCADE,
    document_type document_type NOT NULL,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    mime_type TEXT,
    uploaded_at TIMESTAMPTZ DEFAULT NOW()
)`);
    await queryRunner.query(`CREATE TABLE public.referee_verifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    application_id UUID REFERENCES public.agent_applications(id) ON DELETE CASCADE,
    referee_full_name TEXT NOT NULL,
    referee_whatsapp_number TEXT NOT NULL,
    referee_role TEXT NOT NULL,
    status referee_status DEFAULT 'pending',
    contacted_at TIMESTAMPTZ,
    confirmed_at TIMESTAMPTZ,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
)`);
    await queryRunner.query(`CREATE TABLE public.verification_status_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    application_id UUID REFERENCES public.agent_applications(id) ON DELETE CASCADE,
    previous_status application_status,
    new_status application_status NOT NULL,
    changed_by UUID REFERENCES users(id),
    change_reason TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
)`);
    await queryRunner.query(`INSERT INTO storage.buckets (id, name, public) 
VALUES 
    ('agent-documents', 'agent-documents', false),
    ('agent-id-photos', 'agent-id-photos', false),
    ('agent-selfies', 'agent-selfies', false),
    ('agent-cac-docs', 'agent-cac-docs', false)`);
    await queryRunner.query(`ALTER TABLE public.agent_applications ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE public.agent_profiles ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE public.verification_documents ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE public.referee_verifications ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE public.verification_status_log ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`CREATE POLICY "Anyone can insert applications" ON public.agent_applications
    FOR INSERT WITH CHECK (true)`);
    await queryRunner.query(`CREATE POLICY "Users can view their own applications" ON public.agent_applications
    FOR SELECT USING (true)`);
    await queryRunner.query(`CREATE POLICY "Anyone can view verified agents" ON public.agent_profiles
    FOR SELECT USING (is_active = true)`);
    await queryRunner.query(`CREATE POLICY "Only admins can manage agent profiles" ON public.agent_profiles
    FOR ALL USING (false)`);
    await queryRunner.query(`CREATE POLICY "Only application owner can view documents" ON public.verification_documents
    FOR SELECT USING (true)`);
    await queryRunner.query(`CREATE POLICY "Anyone can view referee status" ON public.referee_verifications
    FOR SELECT USING (true)`);
    await queryRunner.query(`CREATE POLICY "Anyone can view status log" ON public.verification_status_log
    FOR SELECT USING (true)`);
    await queryRunner.query(`CREATE POLICY "Anyone can upload documents" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id IN ('agent-documents', 'agent-id-photos', 'agent-selfies', 'agent-cac-docs'))`);
    await queryRunner.query(`CREATE POLICY "Users can view their uploaded documents" ON storage.objects
    FOR SELECT USING (bucket_id IN ('agent-documents', 'agent-id-photos', 'agent-selfies', 'agent-cac-docs'))`);
    await queryRunner.query(`CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW()`);
    await queryRunner.query(`RETURN NEW`);
    await queryRunner.query(`END`);
    await queryRunner.query(`$$ LANGUAGE plpgsql`);
    await queryRunner.query(`CREATE TRIGGER set_updated_at_agent_applications
    BEFORE UPDATE ON public.agent_applications
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at()`);
    await queryRunner.query(`CREATE TRIGGER set_updated_at_agent_profiles
    BEFORE UPDATE ON public.agent_profiles
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at()`);
    await queryRunner.query(`CREATE TRIGGER set_updated_at_referee_verifications
    BEFORE UPDATE ON public.referee_verifications
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at()`);
    await queryRunner.query(`CREATE OR REPLACE FUNCTION public.generate_agent_id(applicant_name TEXT)
RETURNS TEXT AS $$
DECLARE
    name_part TEXT`);
    await queryRunner.query(`random_part TEXT`);
    await queryRunner.query(`agent_id TEXT`);
    await queryRunner.query(`counter INTEGER := 0`);
    await queryRunner.query(`BEGIN

    name_part := UPPER(REGEXP_REPLACE(SUBSTRING(applicant_name, 1, 5), '[^A-Z]', '', 'g'))`);
    await queryRunner.query(`IF LENGTH(name_part) < 3 THEN
        name_part := RPAD(name_part, 3, 'X')`);
    await queryRunner.query(`END IF`);
    await queryRunner.query(`LOOP
        random_part := LPAD((FLOOR(RANDOM() * 1000))::TEXT, 3, '0')`);
    await queryRunner.query(`agent_id := 'AGT-PHC-' || name_part || random_part`);
    await queryRunner.query(`IF NOT EXISTS (SELECT 1 FROM public.agent_applications WHERE agent_id = agent_id) THEN
            EXIT`);
    await queryRunner.query(`END IF`);
    await queryRunner.query(`counter := counter + 1`);
    await queryRunner.query(`IF counter > 100 THEN

            agent_id := 'AGT-PHC-' || name_part || EXTRACT(EPOCH FROM NOW())::INTEGER`);
    await queryRunner.query(`EXIT`);
    await queryRunner.query(`END IF`);
    await queryRunner.query(`END LOOP`);
    await queryRunner.query(`RETURN agent_id`);
    await queryRunner.query(`END`);
    await queryRunner.query(`$$ LANGUAGE plpgsql`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Rollback queries (auto-generated)
    await queryRunner.query(`DROP FUNCTION IF EXISTS public CASCADE`);
    await queryRunner.query(`DROP FUNCTION IF EXISTS public CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS public.verification_status_log CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS public.referee_verifications CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS public.verification_documents CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS public.agent_profiles CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS public.agent_applications CASCADE`);
  }
}
