import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration57ec16baF72d448d8f298bfcda2fc33420250616042411 implements MigrationInterface {
  name = 'Migration57ec16baF72d448d8f298bfcda2fc33420250616042411';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Converted from Supabase migration: 57ec16baF72d448d8f298bfcda2fc334
    await queryRunner.query(`CREATE TABLE public.agent_commissions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  agent_id TEXT NOT NULL,
  property_id UUID REFERENCES public.properties(id),
  commission_type TEXT NOT NULL DEFAULT 'rental', 
  commission_rate DECIMAL(5,2) NOT NULL, 
  commission_amount INTEGER NOT NULL, 
  transaction_amount INTEGER NOT NULL, 
  status TEXT NOT NULL DEFAULT 'pending', 
  earned_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  paid_date TIMESTAMP WITH TIME ZONE,
  payment_reference TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
)`);
    await queryRunner.query(`CREATE TABLE public.rental_agreements (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  property_id UUID REFERENCES public.properties(id) NOT NULL,
  tenant_id UUID REFERENCES users(id),
  landlord_id UUID REFERENCES users(id) NOT NULL,
  agent_id TEXT,
  agreement_type TEXT NOT NULL DEFAULT 'standard', 
  rent_amount INTEGER NOT NULL, 
  security_deposit INTEGER NOT NULL, 
  lease_start_date DATE NOT NULL,
  lease_end_date DATE NOT NULL,
  lease_duration_months INTEGER NOT NULL,
  agreement_terms JSONB NOT NULL DEFAULT '{}', 
  status TEXT NOT NULL DEFAULT 'draft', 
  generated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  signed_at TIMESTAMP WITH TIME ZONE,
  tenant_signature TEXT,
  landlord_signature TEXT,
  witness_signature TEXT,
  document_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
)`);
    await queryRunner.query(`CREATE TABLE public.maintenance_requests (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  property_id UUID REFERENCES public.properties(id) NOT NULL,
  tenant_id UUID REFERENCES users(id),
  landlord_id UUID REFERENCES users(id),
  agent_id TEXT,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  category TEXT NOT NULL, 
  priority TEXT NOT NULL DEFAULT 'medium', 
  status TEXT NOT NULL DEFAULT 'submitted', 
  urgency_level INTEGER DEFAULT 3, 
  estimated_cost INTEGER, 
  actual_cost INTEGER, 
  contractor_name TEXT,
  contractor_contact TEXT,
  scheduled_date TIMESTAMP WITH TIME ZONE,
  completed_date TIMESTAMP WITH TIME ZONE,
  tenant_satisfaction_rating INTEGER, 
  tenant_feedback TEXT,
  images TEXT[], 
  receipt_images TEXT[], 
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
)`);
    await queryRunner.query(`CREATE INDEX idx_agent_commissions_agent_id ON public.agent_commissions(agent_id)`);
    await queryRunner.query(`CREATE INDEX idx_agent_commissions_property_id ON public.agent_commissions(property_id)`);
    await queryRunner.query(`CREATE INDEX idx_agent_commissions_status ON public.agent_commissions(status)`);
    await queryRunner.query(`CREATE INDEX idx_rental_agreements_property_id ON public.rental_agreements(property_id)`);
    await queryRunner.query(`CREATE INDEX idx_rental_agreements_tenant_id ON public.rental_agreements(tenant_id)`);
    await queryRunner.query(`CREATE INDEX idx_rental_agreements_landlord_id ON public.rental_agreements(landlord_id)`);
    await queryRunner.query(`CREATE INDEX idx_rental_agreements_status ON public.rental_agreements(status)`);
    await queryRunner.query(`CREATE INDEX idx_maintenance_requests_property_id ON public.maintenance_requests(property_id)`);
    await queryRunner.query(`CREATE INDEX idx_maintenance_requests_tenant_id ON public.maintenance_requests(tenant_id)`);
    await queryRunner.query(`CREATE INDEX idx_maintenance_requests_status ON public.maintenance_requests(status)`);
    await queryRunner.query(`CREATE INDEX idx_maintenance_requests_priority ON public.maintenance_requests(priority)`);
    await queryRunner.query(`ALTER TABLE public.agent_commissions ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE public.rental_agreements ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE public.maintenance_requests ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`CREATE POLICY "Agents can view their own commissions" 
  ON public.agent_commissions 
  FOR SELECT 
  USING (
    agent_id = (SELECT agent_id FROM public.profiles WHERE id = auth.uid())
    OR public.has_role(auth.uid(), 'admin')
  )`);
    await queryRunner.query(`CREATE POLICY "Admins can manage all commissions" 
  ON public.agent_commissions 
  FOR ALL 
  USING (public.has_role(auth.uid(), 'admin'))`);
    await queryRunner.query(`CREATE POLICY "Users can view their rental agreements" 
  ON public.rental_agreements 
  FOR SELECT 
  USING (
    tenant_id = auth.uid() 
    OR landlord_id = auth.uid() 
    OR agent_id = (SELECT agent_id FROM public.profiles WHERE id = auth.uid())
    OR public.has_role(auth.uid(), 'admin')
  )`);
    await queryRunner.query(`CREATE POLICY "Landlords and agents can create rental agreements" 
  ON public.rental_agreements 
  FOR INSERT 
  WITH CHECK (
    landlord_id = auth.uid() 
    OR agent_id = (SELECT agent_id FROM public.profiles WHERE id = auth.uid())
    OR public.has_role(auth.uid(), 'admin')
  )`);
    await queryRunner.query(`CREATE POLICY "Landlords and agents can update rental agreements" 
  ON public.rental_agreements 
  FOR UPDATE 
  USING (
    landlord_id = auth.uid() 
    OR agent_id = (SELECT agent_id FROM public.profiles WHERE id = auth.uid())
    OR public.has_role(auth.uid(), 'admin')
  )`);
    await queryRunner.query(`CREATE POLICY "Users can view related maintenance requests" 
  ON public.maintenance_requests 
  FOR SELECT 
  USING (
    tenant_id = auth.uid() 
    OR landlord_id = auth.uid() 
    OR agent_id = (SELECT agent_id FROM public.profiles WHERE id = auth.uid())
    OR public.has_role(auth.uid(), 'admin')
  )`);
    await queryRunner.query(`CREATE POLICY "Tenants can create maintenance requests" 
  ON public.maintenance_requests 
  FOR INSERT 
  WITH CHECK (
    tenant_id = auth.uid()
  )`);
    await queryRunner.query(`CREATE POLICY "Landlords and agents can update maintenance requests" 
  ON public.maintenance_requests 
  FOR UPDATE 
  USING (
    landlord_id = auth.uid() 
    OR agent_id = (SELECT agent_id FROM public.profiles WHERE id = auth.uid())
    OR public.has_role(auth.uid(), 'admin')
  )`);
    await queryRunner.query(`CREATE TRIGGER update_agent_commissions_updated_at 
  BEFORE UPDATE ON public.agent_commissions 
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at()`);
    await queryRunner.query(`CREATE TRIGGER update_rental_agreements_updated_at 
  BEFORE UPDATE ON public.rental_agreements 
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at()`);
    await queryRunner.query(`CREATE TRIGGER update_maintenance_requests_updated_at 
  BEFORE UPDATE ON public.maintenance_requests 
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at()`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Rollback queries (auto-generated)
    await queryRunner.query(`DROP INDEX IF EXISTS idx_maintenance_requests_priority`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_maintenance_requests_status`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_maintenance_requests_tenant_id`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_maintenance_requests_property_id`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_rental_agreements_status`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_rental_agreements_landlord_id`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_rental_agreements_tenant_id`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_rental_agreements_property_id`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_agent_commissions_status`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_agent_commissions_property_id`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_agent_commissions_agent_id`);
    await queryRunner.query(`DROP TABLE IF EXISTS public.maintenance_requests CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS public.rental_agreements CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS public.agent_commissions CASCADE`);
  }
}
