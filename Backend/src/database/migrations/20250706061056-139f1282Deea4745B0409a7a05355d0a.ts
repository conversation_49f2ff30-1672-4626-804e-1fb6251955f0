import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration139f1282Deea4745B0409a7a05355d0a20250706061056 implements MigrationInterface {
  name = 'Migration139f1282Deea4745B0409a7a05355d0a20250706061056';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Converted from Supabase migration: 139f1282Deea4745B0409a7a05355d0a
    await queryRunner.query(`INSERT INTO storage.buckets (id, name, public) 
VALUES ('property-images', 'property-images', true)
ON CONFLICT (id) DO NOTHING`);
    await queryRunner.query(`CREATE POLICY "Anyone can view property images" ON storage.objects
    FOR SELECT USING (bucket_id = 'property-images')`);
    await queryRunner.query(`CREATE POLICY "Authenticated users can upload property images" ON storage.objects
    FOR INSERT TO authenticated 
    WITH CHECK (bucket_id = 'property-images')`);
    await queryRunner.query(`CREATE POLICY "Users can update their own property images" ON storage.objects
    FOR UPDATE TO authenticated
    USING (bucket_id = 'property-images' AND owner = auth.uid())`);
    await queryRunner.query(`CREATE POLICY "Users can delete their own property images" ON storage.objects
    FOR DELETE TO authenticated
    USING (bucket_id = 'property-images' AND owner = auth.uid())`);
    await queryRunner.query(`DROP POLICY IF EXISTS "Owners can update their properties" ON properties`);
    await queryRunner.query(`CREATE POLICY "Property owners and agents can update properties" ON properties
    FOR UPDATE 
    USING (
        landlord_id = auth.uid() 
        OR agent_id IN (
            SELECT agent_id FROM profiles WHERE id = auth.uid()
        )
        OR has_role(auth.uid(), 'admin'::app_role)
        OR has_role(auth.uid(), 'super_admin'::app_role)
    )`);
    await queryRunner.query(`CREATE POLICY "Property owners and admins can delete properties" ON properties
    FOR DELETE 
    USING (
        landlord_id = auth.uid() 
        OR has_role(auth.uid(), 'admin'::app_role)
        OR has_role(auth.uid(), 'super_admin'::app_role)
    )`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Rollback queries (auto-generated)
    
  }
}
