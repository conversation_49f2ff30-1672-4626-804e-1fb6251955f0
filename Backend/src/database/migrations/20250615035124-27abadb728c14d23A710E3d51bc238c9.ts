import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration27abadb728c14d23A710E3d51bc238c920250615035124 implements MigrationInterface {
  name = 'Migration27abadb728c14d23A710E3d51bc238c920250615035124';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Converted from Supabase migration: 27abadb728c14d23A710E3d51bc238c9
    await queryRunner.query(`CREATE TABLE public.escrow_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  property_id UUID REFERENCES public.properties(id) ON DELETE CASCADE,
  landlord_id UUID,
  tenant_email TEXT NOT NULL,
  tenant_name TEXT NOT NULL,
  tenant_phone TEXT,
  amount INTEGER NOT NULL, 
  currency TEXT DEFAULT 'usd',
  transaction_type TEXT DEFAULT 'rent_deposit', 
  status TEXT DEFAULT 'pending', 
  stripe_session_id TEXT UNIQUE,
  stripe_payment_intent_id TEXT,
  escrow_fee INTEGER, 
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  funds_released_at TIMESTAMPTZ,
  dispute_reason TEXT,
  admin_notes TEXT
)`);
    await queryRunner.query(`CREATE TABLE public.escrow_milestones (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id UUID REFERENCES public.escrow_transactions(id) ON DELETE CASCADE,
  milestone_type TEXT NOT NULL, 
  status TEXT DEFAULT 'pending', 
  completed_at TIMESTAMPTZ,
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
)`);
    await queryRunner.query(`ALTER TABLE public.escrow_transactions ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE public.escrow_milestones ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`CREATE POLICY "Users can view their own transactions" 
  ON public.escrow_transactions 
  FOR SELECT 
  USING (user_id = auth.uid() OR tenant_email = auth.email())`);
    await queryRunner.query(`CREATE POLICY "Users can create their own transactions" 
  ON public.escrow_transactions 
  FOR INSERT 
  WITH CHECK (user_id = auth.uid())`);
    await queryRunner.query(`CREATE POLICY "Admins can manage all transactions" 
  ON public.escrow_transactions 
  FOR ALL 
  USING (public.has_role(auth.uid(), 'admin'))`);
    await queryRunner.query(`CREATE POLICY "Users can view milestones for their transactions" 
  ON public.escrow_milestones 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.escrow_transactions 
      WHERE id = transaction_id 
      AND (user_id = auth.uid() OR tenant_email = auth.email())
    )
  )`);
    await queryRunner.query(`CREATE POLICY "System can manage milestones" 
  ON public.escrow_milestones 
  FOR ALL 
  USING (true)`);
    await queryRunner.query(`CREATE TABLE public.saved_searches (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  search_name TEXT NOT NULL,
  search_criteria JSONB NOT NULL,
  alert_frequency TEXT DEFAULT 'daily', 
  is_active BOOLEAN DEFAULT true,
  last_alert_sent TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
)`);
    await queryRunner.query(`CREATE TABLE public.property_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  property_id UUID REFERENCES public.properties(id) ON DELETE CASCADE NOT NULL,
  alert_type TEXT NOT NULL, 
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
)`);
    await queryRunner.query(`ALTER TABLE public.saved_searches ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE public.property_alerts ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`CREATE POLICY "Users can manage their own saved searches" 
  ON public.saved_searches 
  FOR ALL 
  USING (user_id = auth.uid())`);
    await queryRunner.query(`CREATE POLICY "Users can manage their own alerts" 
  ON public.property_alerts 
  FOR ALL 
  USING (user_id = auth.uid())`);
    await queryRunner.query(`CREATE TABLE public.system_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_name TEXT NOT NULL,
  metric_value NUMERIC NOT NULL,
  metric_date DATE NOT NULL DEFAULT CURRENT_DATE,
  additional_data JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
)`);
    await queryRunner.query(`ALTER TABLE public.system_analytics ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`CREATE POLICY "Admins can manage system analytics" 
  ON public.system_analytics 
  FOR ALL 
  USING (public.has_role(auth.uid(), 'admin'))`);
    await queryRunner.query(`CREATE TRIGGER update_escrow_transactions_updated_at
  BEFORE UPDATE ON public.escrow_transactions
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at()`);
    await queryRunner.query(`CREATE TRIGGER update_saved_searches_updated_at
  BEFORE UPDATE ON public.saved_searches
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at()`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Rollback queries (auto-generated)
    await queryRunner.query(`DROP TABLE IF EXISTS public.system_analytics CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS public.property_alerts CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS public.saved_searches CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS public.escrow_milestones CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS public.escrow_transactions CASCADE`);
  }
}
