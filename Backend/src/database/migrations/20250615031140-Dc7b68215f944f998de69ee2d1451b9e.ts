import { MigrationInterface, QueryRunner } from 'typeorm';

export class Dc7b68215f944f998de69ee2d1451b9e20250615031140 implements MigrationInterface {
  name = 'Dc7b68215f944f998de69ee2d1451b9e20250615031140';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Converted from Supabase migration: Dc7b68215f944f998de69ee2d1451b9e
    await queryRunner.query(`INSERT INTO storage.buckets (id, name, public) 
VALUES 
    ('agent-documents', 'agent-documents', false),
    ('agent-id-photos', 'agent-id-photos', false),
    ('agent-selfies', 'agent-selfies', false),
    ('agent-cac-docs', 'agent-cac-docs', false),
    ('property-images', 'property-images', true),
    ('user-avatars', 'user-avatars', true)
ON CONFLICT (id) DO NOTHING`);
    await queryRunner.query(`CREATE POLICY "Authenticated users can upload agent documents" ON storage.objects
    FOR INSERT TO authenticated 
    WITH CHECK (bucket_id IN ('agent-documents', 'agent-id-photos', 'agent-selfies', 'agent-cac-docs'))`);
    await queryRunner.query(`CREATE POLICY "Admins can view all agent documents" ON storage.objects
    FOR SELECT TO authenticated
    USING (
        bucket_id IN ('agent-documents', 'agent-id-photos', 'agent-selfies', 'agent-cac-docs') 
        AND (
            public.has_role(auth.uid(), 'admin'::app_role) OR 
            public.has_role(auth.uid(), 'super_admin'::app_role)
        )
    )`);
    await queryRunner.query(`CREATE POLICY "Anyone can view property images" ON storage.objects
    FOR SELECT USING (bucket_id = 'property-images')`);
    await queryRunner.query(`CREATE POLICY "Authenticated users can upload property images" ON storage.objects
    FOR INSERT TO authenticated 
    WITH CHECK (bucket_id = 'property-images')`);
    await queryRunner.query(`CREATE POLICY "Anyone can view user avatars" ON storage.objects
    FOR SELECT USING (bucket_id = 'user-avatars')`);
    await queryRunner.query(`CREATE POLICY "Users can upload their own avatars" ON storage.objects
    FOR INSERT TO authenticated 
    WITH CHECK (bucket_id = 'user-avatars')`);
    await queryRunner.query(`CREATE POLICY "Admins can delete agent documents" ON storage.objects
    FOR DELETE TO authenticated
    USING (
        bucket_id IN ('agent-documents', 'agent-id-photos', 'agent-selfies', 'agent-cac-docs')
        AND (
            public.has_role(auth.uid(), 'admin'::app_role) OR 
            public.has_role(auth.uid(), 'super_admin'::app_role)
        )
    )`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Rollback queries (auto-generated)
    
  }
}
