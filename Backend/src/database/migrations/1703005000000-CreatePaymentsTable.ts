import { MigrationInterface, QueryRunner, Table, Index, ForeignKey } from 'typeorm';

export class CreatePaymentsTable1703005000000 implements MigrationInterface {
  name = 'CreatePaymentsTable1703005000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum types
    await queryRunner.query(`
      CREATE TYPE "payment_type_enum" AS ENUM('rent', 'deposit', 'commission', 'service_fee', 'subscription')
    `);

    await queryRunner.query(`
      CREATE TYPE "payment_status_enum" AS ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded')
    `);

    await queryRunner.query(`
      CREATE TYPE "payment_gateway_enum" AS ENUM('paystack', 'flutterwave', 'bank_transfer', 'cash')
    `);

    // Create payments table
    await queryRunner.createTable(
      new Table({
        name: 'payments',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'reference',
            type: 'varchar',
            isUnique: true,
            isNullable: false,
          },
          {
            name: 'paymentType',
            type: 'payment_type_enum',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'payment_status_enum',
            default: "'pending'",
          },
          {
            name: 'gateway',
            type: 'payment_gateway_enum',
            isNullable: false,
          },
          {
            name: 'amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'currency',
            type: 'varchar',
            default: "'NGN'",
          },
          {
            name: 'description',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'gatewayReference',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'gatewayResponse',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'dueDate',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'paidAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'failureReason',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'user_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'property_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'recipient_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create indexes using raw SQL
    await queryRunner.query(`CREATE INDEX "IDX_payments_reference" ON "payments" ("reference")`);
    await queryRunner.query(`CREATE INDEX "IDX_payments_status" ON "payments" ("status")`);
    await queryRunner.query(
      `CREATE INDEX "IDX_payments_paymentType" ON "payments" ("paymentType")`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_payments_gateway" ON "payments" ("gateway")`);
    await queryRunner.query(`CREATE INDEX "IDX_payments_user_id" ON "payments" ("user_id")`);
    await queryRunner.query(
      `CREATE INDEX "IDX_payments_property_id" ON "payments" ("property_id")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_payments_recipient_id" ON "payments" ("recipient_id")`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_payments_createdAt" ON "payments" ("createdAt")`);

    // Create foreign keys using raw SQL
    await queryRunner.query(`
      ALTER TABLE "payments"
      ADD CONSTRAINT "FK_payments_user_id"
      FOREIGN KEY ("user_id") REFERENCES "users"("id")
      ON DELETE CASCADE ON UPDATE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "payments"
      ADD CONSTRAINT "FK_payments_property_id"
      FOREIGN KEY ("property_id") REFERENCES "properties"("id")
      ON DELETE SET NULL ON UPDATE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "payments"
      ADD CONSTRAINT "FK_payments_recipient_id"
      FOREIGN KEY ("recipient_id") REFERENCES "users"("id")
      ON DELETE SET NULL ON UPDATE CASCADE
    `);

    // Create trigger for updated_at
    await queryRunner.query(`
      CREATE TRIGGER update_payments_updated_at 
      BEFORE UPDATE ON payments 
      FOR EACH ROW 
      EXECUTE FUNCTION update_updated_at_column();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TRIGGER IF EXISTS update_payments_updated_at ON payments');
    await queryRunner.dropTable('payments');
    await queryRunner.query('DROP TYPE "payment_gateway_enum"');
    await queryRunner.query('DROP TYPE "payment_status_enum"');
    await queryRunner.query('DROP TYPE "payment_type_enum"');
  }
}
