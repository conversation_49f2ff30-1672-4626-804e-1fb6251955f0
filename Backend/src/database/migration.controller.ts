import { Controller, Get, Post, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SupabaseMigrationService } from './supabase-migration.service';

@ApiTags('Database Migration')
@Controller('migration')
export class MigrationController {
  constructor(private readonly migrationService: SupabaseMigrationService) {}

  @Get('status')
  @ApiOperation({
    summary: 'Get migration status',
    description: 'Check the status of Supabase to TypeORM migration process',
  })
  @ApiResponse({
    status: 200,
    description: 'Migration status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        supabase: {
          type: 'object',
          properties: {
            available: { type: 'number', example: 25 },
            files: { type: 'array', items: { type: 'string' } },
          },
        },
        typeorm: {
          type: 'object',
          properties: {
            converted: { type: 'number', example: 25 },
            files: { type: 'array', items: { type: 'string' } },
          },
        },
        executed: {
          type: 'object',
          properties: {
            count: { type: 'number', example: 25 },
            latest: { type: 'string', example: 'CreateUsersTable1703001000000' },
          },
        },
        status: { type: 'string', example: 'ready-for-conversion' },
      },
    },
  })
  async getMigrationStatus() {
    return await this.migrationService.getMigrationStatus();
  }

  @Post('convert-supabase')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Convert Supabase migrations to TypeORM',
    description: 'Convert all Supabase SQL migrations to TypeORM migration classes',
  })
  @ApiResponse({
    status: 200,
    description: 'Supabase migrations converted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Supabase migrations converted successfully' },
        converted: { type: 'number', example: 25 },
        status: { type: 'string', example: 'success' },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Migration conversion failed',
  })
  async convertSupabaseMigrations() {
    try {
      await this.migrationService.convertSupabaseMigrations();
      const status = await this.migrationService.getMigrationStatus();

      return {
        message: 'Supabase migrations converted successfully',
        converted: status.typeorm.converted,
        status: 'success',
      };
    } catch (error) {
      throw new Error(`Migration conversion failed: ${error.message}`);
    }
  }

  @Post('run-migrations')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Run TypeORM migrations',
    description: 'Execute all converted TypeORM migrations to create database schema',
  })
  @ApiResponse({
    status: 200,
    description: 'Migrations executed successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'All migrations executed successfully' },
        status: { type: 'string', example: 'success' },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Migration execution failed',
  })
  async runMigrations() {
    try {
      await this.migrationService.runConvertedMigrations();

      return {
        message: 'All migrations executed successfully',
        status: 'success',
      };
    } catch (error) {
      throw new Error(`Migration execution failed: ${error.message}`);
    }
  }

  @Post('create-schema')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Create comprehensive database schema',
    description: 'Set up database extensions and functions required for the application',
  })
  @ApiResponse({
    status: 200,
    description: 'Database schema created successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Comprehensive database schema created successfully' },
        status: { type: 'string', example: 'success' },
      },
    },
  })
  async createSchema() {
    try {
      await this.migrationService.createComprehensiveSchema();

      return {
        message: 'Comprehensive database schema created successfully',
        status: 'success',
      };
    } catch (error) {
      throw new Error(`Schema creation failed: ${error.message}`);
    }
  }

  @Post('generate-seed-data')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Generate seed data',
    description: 'Generate comprehensive seed data based on the database schema',
  })
  @ApiResponse({
    status: 200,
    description: 'Seed data generated successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Seed data generated successfully' },
        status: { type: 'string', example: 'success' },
      },
    },
  })
  async generateSeedData() {
    try {
      await this.migrationService.generateSeedData();

      return {
        message: 'Seed data generated successfully',
        status: 'success',
      };
    } catch (error) {
      throw new Error(`Seed data generation failed: ${error.message}`);
    }
  }

  @Post('full-migration')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Complete Supabase to Backend migration',
    description:
      'Perform complete migration: convert Supabase migrations, run them, create schema, and generate seed data',
  })
  @ApiResponse({
    status: 200,
    description: 'Full migration completed successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Full Supabase to Backend migration completed successfully',
        },
        steps: {
          type: 'object',
          properties: {
            conversion: { type: 'string', example: 'success' },
            migration: { type: 'string', example: 'success' },
            schema: { type: 'string', example: 'success' },
            seedData: { type: 'string', example: 'success' },
          },
        },
        status: { type: 'string', example: 'success' },
      },
    },
  })
  async fullMigration() {
    const steps = {
      conversion: 'pending',
      migration: 'pending',
      schema: 'pending',
      seedData: 'pending',
    };

    try {
      // Step 1: Convert Supabase migrations
      await this.migrationService.convertSupabaseMigrations();
      steps.conversion = 'success';

      // Step 2: Run migrations
      await this.migrationService.runConvertedMigrations();
      steps.migration = 'success';

      // Step 3: Create comprehensive schema
      await this.migrationService.createComprehensiveSchema();
      steps.schema = 'success';

      // Step 4: Generate seed data
      await this.migrationService.generateSeedData();
      steps.seedData = 'success';

      return {
        message: 'Full Supabase to Backend migration completed successfully',
        steps,
        status: 'success',
      };
    } catch (error) {
      throw new Error(
        `Full migration failed at step: ${JSON.stringify(steps)}. Error: ${error.message}`,
      );
    }
  }
}
