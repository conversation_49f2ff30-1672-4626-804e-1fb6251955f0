import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';

@Injectable()
export class LoggerService implements NestLoggerService {
  private logger: winston.Logger;

  constructor(private configService: ConfigService) {
    this.createLogger();
  }

  private createLogger() {
    const logLevel = this.configService.get('NODE_ENV') === 'production' ? 'info' : 'debug';
    const logDir = './logs';

    // Create transports
    const transports: winston.transport[] = [
      // Console transport
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.colorize(),
          winston.format.printf(({ timestamp, level, message, context, trace }) => {
            const contextStr = context ? `[${context}] ` : '';
            const traceStr = trace ? `\n${trace}` : '';
            return `${timestamp} ${level}: ${contextStr}${message}${traceStr}`;
          }),
        ),
      }),
    ];

    // Add file transports for production
    if (this.configService.get('NODE_ENV') === 'production') {
      // Error logs
      transports.push(
        new DailyRotateFile({
          filename: `${logDir}/error-%DATE%.log`,
          datePattern: 'YYYY-MM-DD',
          level: 'error',
          maxSize: '20m',
          maxFiles: '14d',
          format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
        }),
      );

      // Combined logs
      transports.push(
        new DailyRotateFile({
          filename: `${logDir}/combined-%DATE%.log`,
          datePattern: 'YYYY-MM-DD',
          maxSize: '20m',
          maxFiles: '14d',
          format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
        }),
      );

      // Access logs
      transports.push(
        new DailyRotateFile({
          filename: `${logDir}/access-%DATE%.log`,
          datePattern: 'YYYY-MM-DD',
          maxSize: '20m',
          maxFiles: '30d',
          format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
        }),
      );
    }

    this.logger = winston.createLogger({
      level: logLevel,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json(),
      ),
      defaultMeta: { service: 'phcityrent-api' },
      transports,
    });
  }

  log(message: string, context?: string) {
    this.logger.info(message, { context });
  }

  error(message: string, trace?: string, context?: string) {
    this.logger.error(message, { context, trace });
  }

  warn(message: string, context?: string) {
    this.logger.warn(message, { context });
  }

  debug(message: string, context?: string) {
    this.logger.debug(message, { context });
  }

  verbose(message: string, context?: string) {
    this.logger.verbose(message, { context });
  }

  // Additional methods for structured logging
  logWithMetadata(level: string, message: string, metadata: any, context?: string) {
    this.logger.log(level, message, { ...metadata, context });
  }

  logRequest(method: string, url: string, statusCode: number, responseTime: number, ip: string) {
    this.logger.info('HTTP Request', {
      method,
      url,
      statusCode,
      responseTime,
      ip,
      type: 'http_request',
    });
  }

  logDatabaseQuery(query: string, duration: number, parameters?: any[]) {
    this.logger.debug('Database Query', {
      query,
      duration,
      parameters,
      type: 'database_query',
    });
  }

  logSecurityEvent(event: string, details: any, severity: 'low' | 'medium' | 'high' | 'critical') {
    this.logger.warn('Security Event', {
      event,
      details,
      severity,
      type: 'security_event',
    });
  }

  logBusinessEvent(event: string, userId?: string, details?: any) {
    this.logger.info('Business Event', {
      event,
      userId,
      details,
      type: 'business_event',
    });
  }
}
