import * as moment from 'moment';
import 'moment-timezone';

export class DateUtil {
  /**
   * Get current timestamp
   */
  static now(): Date {
    return new Date();
  }

  /**
   * Add time to a date
   */
  static addTime(date: Date, amount: number, unit: moment.unitOfTime.DurationConstructor): Date {
    return moment(date).add(amount, unit).toDate();
  }

  /**
   * Subtract time from a date
   */
  static subtractTime(
    date: Date,
    amount: number,
    unit: moment.unitOfTime.DurationConstructor,
  ): Date {
    return moment(date).subtract(amount, unit).toDate();
  }

  /**
   * Check if a date is in the past
   */
  static isPast(date: Date): boolean {
    return moment(date).isBefore(moment());
  }

  /**
   * Check if a date is in the future
   */
  static isFuture(date: Date): boolean {
    return moment(date).isAfter(moment());
  }

  /**
   * Format date to string
   */
  static format(date: Date, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
    return moment(date).format(format);
  }

  /**
   * Parse string to date
   */
  static parse(dateString: string, format?: string): Date {
    return format ? moment(dateString, format).toDate() : moment(dateString).toDate();
  }

  /**
   * Get start of day
   */
  static startOfDay(date: Date): Date {
    return moment(date).startOf('day').toDate();
  }

  /**
   * Get end of day
   */
  static endOfDay(date: Date): Date {
    return moment(date).endOf('day').toDate();
  }

  /**
   * Get start of month
   */
  static startOfMonth(date: Date): Date {
    return moment(date).startOf('month').toDate();
  }

  /**
   * Get end of month
   */
  static endOfMonth(date: Date): Date {
    return moment(date).endOf('month').toDate();
  }

  /**
   * Get difference between two dates
   */
  static diff(date1: Date, date2: Date, unit: moment.unitOfTime.Diff = 'milliseconds'): number {
    return moment(date1).diff(moment(date2), unit);
  }

  /**
   * Check if two dates are the same day
   */
  static isSameDay(date1: Date, date2: Date): boolean {
    return moment(date1).isSame(moment(date2), 'day');
  }

  /**
   * Get age from birth date
   */
  static getAge(birthDate: Date): number {
    return moment().diff(moment(birthDate), 'years');
  }

  /**
   * Get timezone offset
   */
  static getTimezoneOffset(timezone: string = 'Africa/Lagos'): number {
    return moment.tz(timezone).utcOffset();
  }

  /**
   * Convert to timezone
   */
  static toTimezone(date: Date, timezone: string = 'Africa/Lagos'): Date {
    return moment(date).tz(timezone).toDate();
  }

  /**
   * Get relative time (e.g., "2 hours ago")
   */
  static fromNow(date: Date): string {
    return moment(date).fromNow();
  }

  /**
   * Get time until date (e.g., "in 2 hours")
   */
  static toNow(date: Date): string {
    return moment(date).toNow();
  }
}
