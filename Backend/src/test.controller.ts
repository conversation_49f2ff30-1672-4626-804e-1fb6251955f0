import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Test')
@Controller('test')
export class TestController {
  @Get('health')
  @ApiOperation({ summary: 'Basic health check' })
  @ApiResponse({
    status: 200,
    description: 'Server is running',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string', example: '2023-12-01T10:00:00Z' },
        uptime: { type: 'number', example: 123.45 },
        message: { type: 'string', example: 'PHCityRent Backend is running successfully!' },
      },
    },
  })
  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      message: 'PHCityRent Backend is running successfully!',
    };
  }

  @Get('endpoints')
  @ApiOperation({ summary: 'List all available endpoints' })
  @ApiResponse({
    status: 200,
    description: 'Available endpoints',
    schema: {
      type: 'object',
      properties: {
        totalEndpoints: { type: 'number', example: 84 },
        modules: {
          type: 'object',
          properties: {
            authentication: { type: 'array', items: { type: 'string' } },
            users: { type: 'array', items: { type: 'string' } },
            properties: { type: 'array', items: { type: 'string' } },
            agents: { type: 'array', items: { type: 'string' } },
            payments: { type: 'array', items: { type: 'string' } },
            analytics: { type: 'array', items: { type: 'string' } },
            admin: { type: 'array', items: { type: 'string' } },
            files: { type: 'array', items: { type: 'string' } },
            notifications: { type: 'array', items: { type: 'string' } },
            health: { type: 'array', items: { type: 'string' } },
          },
        },
      },
    },
  })
  getEndpoints() {
    const endpoints = {
      authentication: [
        'POST /api/v1/auth/register',
        'POST /api/v1/auth/login',
        'POST /api/v1/auth/refresh',
        'POST /api/v1/auth/logout',
        'PATCH /api/v1/auth/change-password',
        'POST /api/v1/auth/forgot-password',
        'POST /api/v1/auth/reset-password',
        'GET /api/v1/auth/me',
      ],
      users: [
        'GET /api/v1/users',
        'GET /api/v1/users/profile',
        'GET /api/v1/users/:id',
        'PATCH /api/v1/users/profile',
        'PATCH /api/v1/users/:id',
        'PATCH /api/v1/users/:id/activate',
        'PATCH /api/v1/users/:id/deactivate',
        'DELETE /api/v1/users/:id',
      ],
      properties: [
        'POST /api/v1/properties',
        'GET /api/v1/properties',
        'GET /api/v1/properties/my-properties',
        'GET /api/v1/properties/stats',
        'GET /api/v1/properties/:id',
        'PATCH /api/v1/properties/:id',
        'PATCH /api/v1/properties/:id/status',
        'PATCH /api/v1/properties/:id/toggle-featured',
        'PATCH /api/v1/properties/:id/toggle-verified',
        'POST /api/v1/properties/:id/inquire',
        'DELETE /api/v1/properties/:id',
      ],
      agents: [
        'GET /api/v1/agents',
        'GET /api/v1/agents/top',
        'GET /api/v1/agents/search',
        'GET /api/v1/agents/:id',
        'GET /api/v1/agents/:id/properties',
        'GET /api/v1/agents/:id/stats',
        'PATCH /api/v1/agents/:id/activate',
        'PATCH /api/v1/agents/:id/deactivate',
      ],
      payments: [
        'POST /api/v1/payments',
        'GET /api/v1/payments',
        'GET /api/v1/payments/stats',
        'GET /api/v1/payments/overdue',
        'GET /api/v1/payments/by-type/:type',
        'GET /api/v1/payments/:id',
        'PATCH /api/v1/payments/:id/status',
        'POST /api/v1/payments/:id/process',
      ],
      analytics: [
        'GET /api/v1/analytics/dashboard',
        'GET /api/v1/analytics/properties',
        'GET /api/v1/analytics/users',
        'GET /api/v1/analytics/payments',
        'GET /api/v1/analytics/market-insights',
      ],
      admin: [
        'GET /api/v1/admin/overview',
        'GET /api/v1/admin/users',
        'GET /api/v1/admin/properties',
        'GET /api/v1/admin/payments',
        'GET /api/v1/admin/pending-approvals',
        'GET /api/v1/admin/recent-activity',
        'PATCH /api/v1/admin/users/:id/suspend',
        'PATCH /api/v1/admin/users/:id/activate',
        'PATCH /api/v1/admin/properties/:id/verify',
        'PATCH /api/v1/admin/properties/:id/unverify',
        'PATCH /api/v1/admin/properties/:id/feature',
        'PATCH /api/v1/admin/properties/:id/unfeature',
        'PATCH /api/v1/admin/payments/:id/approve',
        'PATCH /api/v1/admin/payments/:id/reject',
        'POST /api/v1/admin/bulk-actions',
      ],
      files: [
        'POST /api/v1/files/upload/image',
        'POST /api/v1/files/upload/images',
        'POST /api/v1/files/upload/document',
        'GET /api/v1/files/images/:userId/:filename',
        'GET /api/v1/files/documents/:userId/:filename',
        'GET /api/v1/files/my-files',
        'GET /api/v1/files/my-files/stats',
        'DELETE /api/v1/files/images/:filename',
        'DELETE /api/v1/files/documents/:filename',
      ],
      notifications: [
        'GET /api/v1/notifications',
        'GET /api/v1/notifications/my-notifications',
        'GET /api/v1/notifications/unread-count',
        'GET /api/v1/notifications/stats',
        'GET /api/v1/notifications/by-type/:type',
        'GET /api/v1/notifications/:id',
        'PATCH /api/v1/notifications/:id/read',
        'POST /api/v1/notifications/mark-all-read',
        'POST /api/v1/notifications/retry-failed',
      ],
      health: [
        'GET /api/v1/health',
        'GET /api/v1/health/database',
        'GET /api/v1/health/memory',
        'GET /api/v1/health/disk',
      ],
      test: [
        'GET /api/v1/test/health',
        'GET /api/v1/test/endpoints',
        'GET /api/v1/test/database-schema',
        'GET /api/v1/test/supabase-integration',
      ],
      migration: [
        'GET /api/v1/migration/status',
        'POST /api/v1/migration/convert-supabase',
        'POST /api/v1/migration/run-migrations',
        'POST /api/v1/migration/create-schema',
        'POST /api/v1/migration/generate-seed-data',
        'POST /api/v1/migration/full-migration',
      ],
    };

    const totalEndpoints = Object.values(endpoints).reduce(
      (total, moduleEndpoints) => total + moduleEndpoints.length,
      0,
    );

    return {
      totalEndpoints,
      modules: endpoints,
      summary: {
        totalModules: Object.keys(endpoints).length,
        averageEndpointsPerModule: Math.round(totalEndpoints / Object.keys(endpoints).length),
        mostEndpoints: Math.max(...Object.values(endpoints).map(arr => arr.length)),
        leastEndpoints: Math.min(...Object.values(endpoints).map(arr => arr.length)),
      },
      testing: {
        swaggerUrl: 'http://localhost:3001/api/docs',
        healthCheck: 'http://localhost:3001/api/v1/test/health',
        allEndpoints: 'http://localhost:3001/api/v1/test/endpoints',
        databaseSchema: 'http://localhost:3001/api/v1/test/database-schema',
        supabaseIntegration: 'http://localhost:3001/api/v1/test/supabase-integration',
      },
      message: `PHCityRent API has ${totalEndpoints} endpoints across ${Object.keys(endpoints).length} modules`,
    };
  }

  @Get('database-schema')
  @ApiOperation({ summary: 'Get database schema information' })
  @ApiResponse({
    status: 200,
    description: 'Database schema information',
    schema: {
      type: 'object',
      properties: {
        totalTables: { type: 'number', example: 25 },
        coreEntities: { type: 'array', items: { type: 'string' } },
        supabaseIntegration: { type: 'boolean', example: true },
        migrationStatus: { type: 'string', example: 'ready' },
      },
    },
  })
  getDatabaseSchema() {
    const coreEntities = [
      'users (auth.users)',
      'profiles',
      'properties',
      'property_images',
      'property_documents',
      'property_inspections',
      'property_verification_steps',
      'virtual_tours',
      'property_inquiries',
      'property_views',
      'property_analytics_detailed',
      'payment_transactions',
      'payment_webhooks',
      'payment_plans',
      'payment_refunds',
      'agent_applications',
      'agent_performance_metrics',
      'rental_applications',
      'maintenance_requests',
      'notifications',
      'user_preferences',
      'saved_searches',
      'property_market_data',
      'ai_recommendations',
      'communication_threads',
    ];

    const supabaseFeatures = [
      'Row Level Security (RLS)',
      'Real-time subscriptions',
      'Storage buckets for files',
      'Edge functions',
      'Authentication integration',
      'Comprehensive indexing',
      'Advanced analytics views',
      'Geolocation search',
      'Payment processing',
      'AI-powered recommendations',
    ];

    return {
      totalTables: coreEntities.length,
      coreEntities,
      supabaseFeatures,
      supabaseIntegration: true,
      migrationStatus: 'ready',
      indexCount: '84+ strategic indexes',
      performanceOptimizations: [
        'Composite indexes for complex queries',
        'Partial indexes for filtered data',
        'GIN indexes for full-text search',
        'Spatial indexes for location queries',
        'Connection pooling',
        'Query optimization views',
      ],
      message:
        'PHCityRent uses a comprehensive Supabase-powered database with enterprise-grade optimization',
    };
  }

  @Get('supabase-integration')
  @ApiOperation({ summary: 'Get Supabase integration details' })
  @ApiResponse({
    status: 200,
    description: 'Supabase integration information',
    schema: {
      type: 'object',
      properties: {
        migrationsAvailable: { type: 'number', example: 25 },
        functionsAvailable: { type: 'number', example: 15 },
        storageIntegration: { type: 'boolean', example: true },
        realtimeEnabled: { type: 'boolean', example: true },
      },
    },
  })
  getSupabaseIntegration() {
    const availableMigrations = [
      '20250614095150 - Initial schema setup',
      '20250614101722 - User profiles and authentication',
      '20250614120000 - Storage buckets creation',
      '20250615031140 - Properties table enhancement',
      '20250615031749 - Property inquiries system',
      '20250615035124 - Agent applications system',
      '20250615064000 - Payment processing tables',
      '20250615081747 - Analytics and reporting',
      '20250615083356 - Notification system',
      '20250615192809 - Real-time features',
      '20250616042411 - Advanced search capabilities',
      '20250706000001 - Quick actions enhancement',
      '20250706061056 - Performance optimization',
      '20250708000000 - Payment transactions',
      '20250709000000 - Real-time infrastructure',
      '20250709000001 - Comprehensive database optimization',
      '20250709000002 - Agent management tables',
      '20250709000003 - Property management tables',
      '20250709000004 - AI features tables',
      '20250709000005 - Analytics reporting system',
      '20250709000006 - Communication system',
      '20250709000007 - Third party integrations',
      '20250709000008 - Performance optimization',
      '20250709000009 - Security hardening',
      '20250710000001 - Production database optimization',
    ];

    const availableFunctions = [
      'create-escrow-payment - Secure payment processing',
      'send-notification - Multi-channel notifications',
      'search_properties_by_location - Geolocation search',
      'get_search_suggestions - Smart search suggestions',
      'get_search_facets - Dynamic search filters',
      'get_property_analytics - Property performance metrics',
      'get_market_data - Real estate market analysis',
      'get_property_recommendations - AI-powered recommendations',
      'setup_property_verification_steps - Automated verification',
      'calculate_agent_commission - Commission calculations',
      'process_rental_application - Application processing',
      'generate_property_report - Automated reporting',
      'update_property_analytics - Real-time analytics',
      'manage_property_visibility - Dynamic listing management',
      'sync_payment_status - Payment synchronization',
    ];

    const storageIntegration = {
      buckets: [
        'property-images - Public property photos',
        'property-documents - Private property documents',
        'virtual-tours - Interactive property tours',
        'inspection-photos - Property inspection images',
        'user-avatars - User profile pictures',
        'agent-documents - Agent verification documents',
      ],
      policies: 'Row Level Security enabled for all buckets',
      features: ['Automatic image optimization', 'CDN distribution', 'Secure file access'],
    };

    return {
      migrationsAvailable: availableMigrations.length,
      availableMigrations,
      functionsAvailable: availableFunctions.length,
      availableFunctions,
      storageIntegration,
      realtimeEnabled: true,
      realtimeFeatures: [
        'Property view tracking',
        'Live inquiry notifications',
        'Real-time chat system',
        'Property status updates',
        'Payment status notifications',
        'Agent activity tracking',
      ],
      integrationStatus: 'fully-compatible',
      message: 'NestJS backend is fully compatible with existing Supabase infrastructure',
    };
  }
}
