export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.12 (cd3cf9e)"
  }
  public: {
    Tables: {
      admin_performance: {
        Row: {
          additional_data: Json | null
          admin_id: string | null
          created_at: string | null
          id: string
          metric_date: string | null
          metric_name: string
          metric_value: number
        }
        Insert: {
          additional_data?: Json | null
          admin_id?: string | null
          created_at?: string | null
          id?: string
          metric_date?: string | null
          metric_name: string
          metric_value: number
        }
        Update: {
          additional_data?: Json | null
          admin_id?: string | null
          created_at?: string | null
          id?: string
          metric_date?: string | null
          metric_name?: string
          metric_value?: number
        }
        Relationships: []
      }
      agent_applications: {
        Row: {
          agent_id: string
          created_at: string | null
          email: string | null
          estimated_completion: string | null
          full_name: string
          id: string
          is_registered_business: boolean | null
          next_action: string | null
          operating_areas: string[]
          residential_address: string
          reviewer_notes: string | null
          status: Database["public"]["Enums"]["application_status"] | null
          updated_at: string | null
          whatsapp_number: string
        }
        Insert: {
          agent_id: string
          created_at?: string | null
          email?: string | null
          estimated_completion?: string | null
          full_name: string
          id?: string
          is_registered_business?: boolean | null
          next_action?: string | null
          operating_areas: string[]
          residential_address: string
          reviewer_notes?: string | null
          status?: Database["public"]["Enums"]["application_status"] | null
          updated_at?: string | null
          whatsapp_number: string
        }
        Update: {
          agent_id?: string
          created_at?: string | null
          email?: string | null
          estimated_completion?: string | null
          full_name?: string
          id?: string
          is_registered_business?: boolean | null
          next_action?: string | null
          operating_areas?: string[]
          residential_address?: string
          reviewer_notes?: string | null
          status?: Database["public"]["Enums"]["application_status"] | null
          updated_at?: string | null
          whatsapp_number?: string
        }
        Relationships: []
      }
      agent_commissions: {
        Row: {
          agent_id: string
          commission_amount: number
          commission_rate: number
          commission_type: string
          created_at: string
          earned_date: string
          id: string
          notes: string | null
          paid_date: string | null
          payment_reference: string | null
          property_id: string | null
          status: string
          transaction_amount: number
          updated_at: string
        }
        Insert: {
          agent_id: string
          commission_amount: number
          commission_rate: number
          commission_type?: string
          created_at?: string
          earned_date?: string
          id?: string
          notes?: string | null
          paid_date?: string | null
          payment_reference?: string | null
          property_id?: string | null
          status?: string
          transaction_amount: number
          updated_at?: string
        }
        Update: {
          agent_id?: string
          commission_amount?: number
          commission_rate?: number
          commission_type?: string
          created_at?: string
          earned_date?: string
          id?: string
          notes?: string | null
          paid_date?: string | null
          payment_reference?: string | null
          property_id?: string | null
          status?: string
          transaction_amount?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "agent_commissions_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_profiles: {
        Row: {
          agent_id: string
          application_id: string | null
          created_at: string | null
          email: string | null
          full_name: string
          id: string
          is_active: boolean | null
          operating_areas: string[]
          updated_at: string | null
          user_id: string | null
          verification_date: string | null
          whatsapp_number: string
        }
        Insert: {
          agent_id: string
          application_id?: string | null
          created_at?: string | null
          email?: string | null
          full_name: string
          id?: string
          is_active?: boolean | null
          operating_areas: string[]
          updated_at?: string | null
          user_id?: string | null
          verification_date?: string | null
          whatsapp_number: string
        }
        Update: {
          agent_id?: string
          application_id?: string | null
          created_at?: string | null
          email?: string | null
          full_name?: string
          id?: string
          is_active?: boolean | null
          operating_areas?: string[]
          updated_at?: string | null
          user_id?: string | null
          verification_date?: string | null
          whatsapp_number?: string
        }
        Relationships: [
          {
            foreignKeyName: "agent_profiles_application_id_fkey"
            columns: ["application_id"]
            isOneToOne: false
            referencedRelation: "agent_applications"
            referencedColumns: ["id"]
          },
        ]
      }
      amenities: {
        Row: {
          affects_price: boolean | null
          category_id: string | null
          created_at: string
          description: string | null
          display_order: number | null
          icon: string | null
          id: string
          is_active: boolean | null
          is_premium: boolean | null
          name: string
        }
        Insert: {
          affects_price?: boolean | null
          category_id?: string | null
          created_at?: string
          description?: string | null
          display_order?: number | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          is_premium?: boolean | null
          name: string
        }
        Update: {
          affects_price?: boolean | null
          category_id?: string | null
          created_at?: string
          description?: string | null
          display_order?: number | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          is_premium?: boolean | null
          name?: string
        }
        Relationships: [
          {
            foreignKeyName: "amenities_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "amenity_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      amenity_categories: {
        Row: {
          created_at: string
          description: string | null
          display_order: number | null
          icon: string | null
          id: string
          is_active: boolean | null
          name: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          display_order?: number | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          name: string
        }
        Update: {
          created_at?: string
          description?: string | null
          display_order?: number | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
        }
        Relationships: []
      }
      bulk_operations_log: {
        Row: {
          admin_id: string | null
          application_ids: string[]
          completed_at: string | null
          completed_count: number | null
          created_at: string | null
          error_details: Json | null
          failed_count: number | null
          id: string
          operation_data: Json | null
          operation_type: string
          status: string | null
        }
        Insert: {
          admin_id?: string | null
          application_ids: string[]
          completed_at?: string | null
          completed_count?: number | null
          created_at?: string | null
          error_details?: Json | null
          failed_count?: number | null
          id?: string
          operation_data?: Json | null
          operation_type: string
          status?: string | null
        }
        Update: {
          admin_id?: string | null
          application_ids?: string[]
          completed_at?: string | null
          completed_count?: number | null
          created_at?: string | null
          error_details?: Json | null
          failed_count?: number | null
          id?: string
          operation_data?: Json | null
          operation_type?: string
          status?: string | null
        }
        Relationships: []
      }
      document_validation: {
        Row: {
          confidence_score: number | null
          document_id: string | null
          id: string
          validated_at: string | null
          validated_by: string | null
          validation_details: Json | null
          validation_status: string
          validation_type: string
        }
        Insert: {
          confidence_score?: number | null
          document_id?: string | null
          id?: string
          validated_at?: string | null
          validated_by?: string | null
          validation_details?: Json | null
          validation_status: string
          validation_type: string
        }
        Update: {
          confidence_score?: number | null
          document_id?: string | null
          id?: string
          validated_at?: string | null
          validated_by?: string | null
          validation_details?: Json | null
          validation_status?: string
          validation_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "document_validation_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "verification_documents"
            referencedColumns: ["id"]
          },
        ]
      }
      escrow_milestones: {
        Row: {
          completed_at: string | null
          created_at: string
          id: string
          milestone_type: string
          notes: string | null
          status: string | null
          transaction_id: string | null
        }
        Insert: {
          completed_at?: string | null
          created_at?: string
          id?: string
          milestone_type: string
          notes?: string | null
          status?: string | null
          transaction_id?: string | null
        }
        Update: {
          completed_at?: string | null
          created_at?: string
          id?: string
          milestone_type?: string
          notes?: string | null
          status?: string | null
          transaction_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "escrow_milestones_transaction_id_fkey"
            columns: ["transaction_id"]
            isOneToOne: false
            referencedRelation: "escrow_transactions"
            referencedColumns: ["id"]
          },
        ]
      }
      escrow_transactions: {
        Row: {
          admin_notes: string | null
          amount: number
          created_at: string
          currency: string | null
          dispute_reason: string | null
          escrow_fee: number | null
          funds_released_at: string | null
          id: string
          landlord_id: string | null
          property_id: string | null
          status: string | null
          stripe_payment_intent_id: string | null
          stripe_session_id: string | null
          tenant_email: string
          tenant_name: string
          tenant_phone: string | null
          transaction_type: string | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          admin_notes?: string | null
          amount: number
          created_at?: string
          currency?: string | null
          dispute_reason?: string | null
          escrow_fee?: number | null
          funds_released_at?: string | null
          id?: string
          landlord_id?: string | null
          property_id?: string | null
          status?: string | null
          stripe_payment_intent_id?: string | null
          stripe_session_id?: string | null
          tenant_email: string
          tenant_name: string
          tenant_phone?: string | null
          transaction_type?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          admin_notes?: string | null
          amount?: number
          created_at?: string
          currency?: string | null
          dispute_reason?: string | null
          escrow_fee?: number | null
          funds_released_at?: string | null
          id?: string
          landlord_id?: string | null
          property_id?: string | null
          status?: string | null
          stripe_payment_intent_id?: string | null
          stripe_session_id?: string | null
          tenant_email?: string
          tenant_name?: string
          tenant_phone?: string | null
          transaction_type?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "escrow_transactions_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      maintenance_requests: {
        Row: {
          actual_cost: number | null
          agent_id: string | null
          category: string
          completed_date: string | null
          contractor_contact: string | null
          contractor_name: string | null
          created_at: string
          description: string
          estimated_cost: number | null
          id: string
          images: string[] | null
          landlord_id: string | null
          priority: string
          property_id: string
          receipt_images: string[] | null
          scheduled_date: string | null
          status: string
          tenant_feedback: string | null
          tenant_id: string | null
          tenant_satisfaction_rating: number | null
          title: string
          updated_at: string
          urgency_level: number | null
        }
        Insert: {
          actual_cost?: number | null
          agent_id?: string | null
          category: string
          completed_date?: string | null
          contractor_contact?: string | null
          contractor_name?: string | null
          created_at?: string
          description: string
          estimated_cost?: number | null
          id?: string
          images?: string[] | null
          landlord_id?: string | null
          priority?: string
          property_id: string
          receipt_images?: string[] | null
          scheduled_date?: string | null
          status?: string
          tenant_feedback?: string | null
          tenant_id?: string | null
          tenant_satisfaction_rating?: number | null
          title: string
          updated_at?: string
          urgency_level?: number | null
        }
        Update: {
          actual_cost?: number | null
          agent_id?: string | null
          category?: string
          completed_date?: string | null
          contractor_contact?: string | null
          contractor_name?: string | null
          created_at?: string
          description?: string
          estimated_cost?: number | null
          id?: string
          images?: string[] | null
          landlord_id?: string | null
          priority?: string
          property_id?: string
          receipt_images?: string[] | null
          scheduled_date?: string | null
          status?: string
          tenant_feedback?: string | null
          tenant_id?: string | null
          tenant_satisfaction_rating?: number | null
          title?: string
          updated_at?: string
          urgency_level?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "maintenance_requests_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      messages: {
        Row: {
          content: string
          created_at: string
          id: string
          is_read: boolean
          property_id: string | null
          recipient_id: string
          sender_id: string
          subject: string | null
          updated_at: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          is_read?: boolean
          property_id?: string | null
          recipient_id: string
          sender_id: string
          subject?: string | null
          updated_at?: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          is_read?: boolean
          property_id?: string | null
          recipient_id?: string
          sender_id?: string
          subject?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "messages_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      notification_log: {
        Row: {
          application_id: string | null
          created_at: string | null
          delivered_at: string | null
          error_message: string | null
          id: string
          message: string
          notification_type: string
          recipient_number: string
          recipient_type: string
          sent_at: string | null
          status: string | null
        }
        Insert: {
          application_id?: string | null
          created_at?: string | null
          delivered_at?: string | null
          error_message?: string | null
          id?: string
          message: string
          notification_type: string
          recipient_number: string
          recipient_type: string
          sent_at?: string | null
          status?: string | null
        }
        Update: {
          application_id?: string | null
          created_at?: string | null
          delivered_at?: string | null
          error_message?: string | null
          id?: string
          message?: string
          notification_type?: string
          recipient_number?: string
          recipient_type?: string
          sent_at?: string | null
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notification_log_application_id_fkey"
            columns: ["application_id"]
            isOneToOne: false
            referencedRelation: "agent_applications"
            referencedColumns: ["id"]
          },
        ]
      }
      payment_history: {
        Row: {
          amount: number
          created_at: string
          due_date: string | null
          id: string
          landlord_id: string
          notes: string | null
          paid_date: string | null
          payment_method: string
          payment_status: string | null
          payment_type: string
          rental_agreement_id: string | null
          tenant_id: string
          transaction_reference: string | null
          updated_at: string
        }
        Insert: {
          amount: number
          created_at?: string
          due_date?: string | null
          id?: string
          landlord_id: string
          notes?: string | null
          paid_date?: string | null
          payment_method: string
          payment_status?: string | null
          payment_type: string
          rental_agreement_id?: string | null
          tenant_id: string
          transaction_reference?: string | null
          updated_at?: string
        }
        Update: {
          amount?: number
          created_at?: string
          due_date?: string | null
          id?: string
          landlord_id?: string
          notes?: string | null
          paid_date?: string | null
          payment_method?: string
          payment_status?: string | null
          payment_type?: string
          rental_agreement_id?: string | null
          tenant_id?: string
          transaction_reference?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payment_history_rental_agreement_id_fkey"
            columns: ["rental_agreement_id"]
            isOneToOne: false
            referencedRelation: "rental_agreements"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          agent_id: string | null
          created_at: string | null
          email: string | null
          full_name: string | null
          id: string
          is_verified_agent: boolean | null
          updated_at: string | null
          whatsapp_number: string | null
        }
        Insert: {
          agent_id?: string | null
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id: string
          is_verified_agent?: boolean | null
          updated_at?: string | null
          whatsapp_number?: string | null
        }
        Update: {
          agent_id?: string | null
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id?: string
          is_verified_agent?: boolean | null
          updated_at?: string | null
          whatsapp_number?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: true
            referencedRelation: "agent_applications"
            referencedColumns: ["agent_id"]
          },
        ]
      }
      properties: {
        Row: {
          agent_id: string | null
          amenities: string[] | null
          area_sqft: number | null
          bathrooms: number
          bedrooms: number
          contact_email: string | null
          contact_whatsapp: string | null
          created_at: string | null
          description: string | null
          featured: boolean | null
          id: string
          images: string[] | null
          is_available: boolean | null
          is_verified: boolean | null
          landlord_id: string | null
          location: string
          price_per_month: number | null
          price_per_year: number
          property_type: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          agent_id?: string | null
          amenities?: string[] | null
          area_sqft?: number | null
          bathrooms?: number
          bedrooms?: number
          contact_email?: string | null
          contact_whatsapp?: string | null
          created_at?: string | null
          description?: string | null
          featured?: boolean | null
          id?: string
          images?: string[] | null
          is_available?: boolean | null
          is_verified?: boolean | null
          landlord_id?: string | null
          location: string
          price_per_month?: number | null
          price_per_year: number
          property_type?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          agent_id?: string | null
          amenities?: string[] | null
          area_sqft?: number | null
          bathrooms?: number
          bedrooms?: number
          contact_email?: string | null
          contact_whatsapp?: string | null
          created_at?: string | null
          description?: string | null
          featured?: boolean | null
          id?: string
          images?: string[] | null
          is_available?: boolean | null
          is_verified?: boolean | null
          landlord_id?: string | null
          location?: string
          price_per_month?: number | null
          price_per_year?: number
          property_type?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "properties_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agent_applications"
            referencedColumns: ["agent_id"]
          },
        ]
      }
      property_alerts: {
        Row: {
          alert_type: string
          created_at: string
          id: string
          is_read: boolean | null
          property_id: string
          user_id: string
        }
        Insert: {
          alert_type: string
          created_at?: string
          id?: string
          is_read?: boolean | null
          property_id: string
          user_id: string
        }
        Update: {
          alert_type?: string
          created_at?: string
          id?: string
          is_read?: boolean | null
          property_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "property_alerts_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      property_analytics: {
        Row: {
          average_response_time_ms: number | null
          created_at: string
          id: string
          inquiries_count: number | null
          inquiry_conversion_rate: number | null
          is_featured: boolean | null
          is_verified: boolean | null
          last_30_days_views: number | null
          last_7_days_views: number | null
          property_id: string
          response_rate: number | null
          total_amenities: number | null
          total_images: number | null
          updated_at: string
          views_count: number | null
        }
        Insert: {
          average_response_time_ms?: number | null
          created_at?: string
          id?: string
          inquiries_count?: number | null
          inquiry_conversion_rate?: number | null
          is_featured?: boolean | null
          is_verified?: boolean | null
          last_30_days_views?: number | null
          last_7_days_views?: number | null
          property_id: string
          response_rate?: number | null
          total_amenities?: number | null
          total_images?: number | null
          updated_at?: string
          views_count?: number | null
        }
        Update: {
          average_response_time_ms?: number | null
          created_at?: string
          id?: string
          inquiries_count?: number | null
          inquiry_conversion_rate?: number | null
          is_featured?: boolean | null
          is_verified?: boolean | null
          last_30_days_views?: number | null
          last_7_days_views?: number | null
          property_id?: string
          response_rate?: number | null
          total_amenities?: number | null
          total_images?: number | null
          updated_at?: string
          views_count?: number | null
        }
        Relationships: []
      }
      property_availability: {
        Row: {
          available_from: string
          available_until: string | null
          created_at: string
          id: string
          is_available: boolean | null
          price_override: number | null
          property_id: string
          reason_unavailable: string | null
          updated_at: string
        }
        Insert: {
          available_from: string
          available_until?: string | null
          created_at?: string
          id?: string
          is_available?: boolean | null
          price_override?: number | null
          property_id: string
          reason_unavailable?: string | null
          updated_at?: string
        }
        Update: {
          available_from?: string
          available_until?: string | null
          created_at?: string
          id?: string
          is_available?: boolean | null
          price_override?: number | null
          property_id?: string
          reason_unavailable?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "property_availability_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      property_documents: {
        Row: {
          created_at: string
          document_name: string
          document_type: string
          expiry_date: string | null
          file_path: string
          file_size: number | null
          id: string
          is_public: boolean | null
          mime_type: string | null
          property_id: string
          updated_at: string
          uploaded_by: string | null
        }
        Insert: {
          created_at?: string
          document_name: string
          document_type: string
          expiry_date?: string | null
          file_path: string
          file_size?: number | null
          id?: string
          is_public?: boolean | null
          mime_type?: string | null
          property_id: string
          updated_at?: string
          uploaded_by?: string | null
        }
        Update: {
          created_at?: string
          document_name?: string
          document_type?: string
          expiry_date?: string | null
          file_path?: string
          file_size?: number | null
          id?: string
          is_public?: boolean | null
          mime_type?: string | null
          property_id?: string
          updated_at?: string
          uploaded_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "property_documents_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      property_images: {
        Row: {
          alt_text: string | null
          created_at: string
          file_size: number | null
          file_type: string | null
          height: number | null
          id: string
          is_primary: boolean | null
          order_index: number
          property_id: string
          thumbnail_url: string | null
          url: string
          width: number | null
        }
        Insert: {
          alt_text?: string | null
          created_at?: string
          file_size?: number | null
          file_type?: string | null
          height?: number | null
          id?: string
          is_primary?: boolean | null
          order_index?: number
          property_id: string
          thumbnail_url?: string | null
          url: string
          width?: number | null
        }
        Update: {
          alt_text?: string | null
          created_at?: string
          file_size?: number | null
          file_type?: string | null
          height?: number | null
          id?: string
          is_primary?: boolean | null
          order_index?: number
          property_id?: string
          thumbnail_url?: string | null
          url?: string
          width?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_property_images_property_id"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      property_inquiries: {
        Row: {
          created_at: string | null
          id: string
          inquirer_email: string
          inquirer_name: string
          inquirer_phone: string | null
          inquiry_type: string | null
          message: string | null
          property_id: string | null
          responded_at: string | null
          status: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          inquirer_email: string
          inquirer_name: string
          inquirer_phone?: string | null
          inquiry_type?: string | null
          message?: string | null
          property_id?: string | null
          responded_at?: string | null
          status?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          inquirer_email?: string
          inquirer_name?: string
          inquirer_phone?: string | null
          inquiry_type?: string | null
          message?: string | null
          property_id?: string | null
          responded_at?: string | null
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "property_inquiries_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      property_market_data: {
        Row: {
          available_properties: number
          average_days_on_market: number
          average_price: number
          competition_level: string | null
          created_at: string
          demand_score: number | null
          id: string
          location: string
          median_price: number
          price_per_sqft: number
          price_trend: string | null
          property_type: string
          total_properties: number
          updated_at: string
        }
        Insert: {
          available_properties?: number
          average_days_on_market?: number
          average_price: number
          competition_level?: string | null
          created_at?: string
          demand_score?: number | null
          id?: string
          location: string
          median_price: number
          price_per_sqft: number
          price_trend?: string | null
          property_type: string
          total_properties?: number
          updated_at?: string
        }
        Update: {
          available_properties?: number
          average_days_on_market?: number
          average_price?: number
          competition_level?: string | null
          created_at?: string
          demand_score?: number | null
          id?: string
          location?: string
          median_price?: number
          price_per_sqft?: number
          price_trend?: string | null
          property_type?: string
          total_properties?: number
          updated_at?: string
        }
        Relationships: []
      }
      property_recommendations: {
        Row: {
          agent_id: string | null
          amenities: string[] | null
          area_sqft: number | null
          bathrooms: number
          bedrooms: number
          contact_email: string | null
          contact_whatsapp: string | null
          created_at: string
          description: string | null
          id: string
          images: string[] | null
          is_available: boolean | null
          is_verified: boolean | null
          location: string
          price_per_month: number | null
          price_per_year: number
          property_id: string
          property_type: string | null
          reason: string
          recommendation_score: number
          title: string
          user_id: string
        }
        Insert: {
          agent_id?: string | null
          amenities?: string[] | null
          area_sqft?: number | null
          bathrooms?: number
          bedrooms?: number
          contact_email?: string | null
          contact_whatsapp?: string | null
          created_at?: string
          description?: string | null
          id?: string
          images?: string[] | null
          is_available?: boolean | null
          is_verified?: boolean | null
          location: string
          price_per_month?: number | null
          price_per_year: number
          property_id: string
          property_type?: string | null
          reason: string
          recommendation_score?: number
          title: string
          user_id: string
        }
        Update: {
          agent_id?: string | null
          amenities?: string[] | null
          area_sqft?: number | null
          bathrooms?: number
          bedrooms?: number
          contact_email?: string | null
          contact_whatsapp?: string | null
          created_at?: string
          description?: string | null
          id?: string
          images?: string[] | null
          is_available?: boolean | null
          is_verified?: boolean | null
          location?: string
          price_per_month?: number | null
          price_per_year?: number
          property_id?: string
          property_type?: string | null
          reason?: string
          recommendation_score?: number
          title?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_property_recommendations_property_id"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      property_reviews: {
        Row: {
          amenities_rating: number | null
          created_at: string
          id: string
          is_anonymous: boolean | null
          is_verified: boolean | null
          landlord_rating: number | null
          location_rating: number | null
          property_id: string
          rating: number
          review_text: string | null
          updated_at: string
          user_id: string
          value_rating: number | null
        }
        Insert: {
          amenities_rating?: number | null
          created_at?: string
          id?: string
          is_anonymous?: boolean | null
          is_verified?: boolean | null
          landlord_rating?: number | null
          location_rating?: number | null
          property_id: string
          rating: number
          review_text?: string | null
          updated_at?: string
          user_id: string
          value_rating?: number | null
        }
        Update: {
          amenities_rating?: number | null
          created_at?: string
          id?: string
          is_anonymous?: boolean | null
          is_verified?: boolean | null
          landlord_rating?: number | null
          location_rating?: number | null
          property_id?: string
          rating?: number
          review_text?: string | null
          updated_at?: string
          user_id?: string
          value_rating?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "property_reviews_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      property_viewings: {
        Row: {
          agent_id: string | null
          attendance_confirmed: boolean | null
          created_at: string
          duration_minutes: number | null
          feedback: string | null
          id: string
          notes: string | null
          property_id: string
          scheduled_date: string
          status: string | null
          updated_at: string
          user_id: string
          viewing_type: string | null
        }
        Insert: {
          agent_id?: string | null
          attendance_confirmed?: boolean | null
          created_at?: string
          duration_minutes?: number | null
          feedback?: string | null
          id?: string
          notes?: string | null
          property_id: string
          scheduled_date: string
          status?: string | null
          updated_at?: string
          user_id: string
          viewing_type?: string | null
        }
        Update: {
          agent_id?: string | null
          attendance_confirmed?: boolean | null
          created_at?: string
          duration_minutes?: number | null
          feedback?: string | null
          id?: string
          notes?: string | null
          property_id?: string
          scheduled_date?: string
          status?: string | null
          updated_at?: string
          user_id?: string
          viewing_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "property_viewings_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      property_watchlist: {
        Row: {
          created_at: string
          id: string
          notes: string | null
          notification_preferences: Json | null
          price_alert_threshold: number | null
          property_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          notes?: string | null
          notification_preferences?: Json | null
          price_alert_threshold?: number | null
          property_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          notes?: string | null
          notification_preferences?: Json | null
          price_alert_threshold?: number | null
          property_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "property_watchlist_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      referee_verifications: {
        Row: {
          application_id: string | null
          confirmed_at: string | null
          contacted_at: string | null
          created_at: string | null
          id: string
          notes: string | null
          referee_full_name: string
          referee_role: string
          referee_whatsapp_number: string
          status: Database["public"]["Enums"]["referee_status"] | null
          updated_at: string | null
        }
        Insert: {
          application_id?: string | null
          confirmed_at?: string | null
          contacted_at?: string | null
          created_at?: string | null
          id?: string
          notes?: string | null
          referee_full_name: string
          referee_role: string
          referee_whatsapp_number: string
          status?: Database["public"]["Enums"]["referee_status"] | null
          updated_at?: string | null
        }
        Update: {
          application_id?: string | null
          confirmed_at?: string | null
          contacted_at?: string | null
          created_at?: string | null
          id?: string
          notes?: string | null
          referee_full_name?: string
          referee_role?: string
          referee_whatsapp_number?: string
          status?: Database["public"]["Enums"]["referee_status"] | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "referee_verifications_application_id_fkey"
            columns: ["application_id"]
            isOneToOne: false
            referencedRelation: "agent_applications"
            referencedColumns: ["id"]
          },
        ]
      }
      rental_agreements: {
        Row: {
          agent_id: string | null
          agreement_terms: Json
          agreement_type: string
          created_at: string
          document_url: string | null
          generated_at: string
          id: string
          landlord_id: string
          landlord_signature: string | null
          lease_duration_months: number
          lease_end_date: string
          lease_start_date: string
          property_id: string
          rent_amount: number
          security_deposit: number
          signed_at: string | null
          status: string
          tenant_id: string | null
          tenant_signature: string | null
          updated_at: string
          witness_signature: string | null
        }
        Insert: {
          agent_id?: string | null
          agreement_terms?: Json
          agreement_type?: string
          created_at?: string
          document_url?: string | null
          generated_at?: string
          id?: string
          landlord_id: string
          landlord_signature?: string | null
          lease_duration_months: number
          lease_end_date: string
          lease_start_date: string
          property_id: string
          rent_amount: number
          security_deposit: number
          signed_at?: string | null
          status?: string
          tenant_id?: string | null
          tenant_signature?: string | null
          updated_at?: string
          witness_signature?: string | null
        }
        Update: {
          agent_id?: string | null
          agreement_terms?: Json
          agreement_type?: string
          created_at?: string
          document_url?: string | null
          generated_at?: string
          id?: string
          landlord_id?: string
          landlord_signature?: string | null
          lease_duration_months?: number
          lease_end_date?: string
          lease_start_date?: string
          property_id?: string
          rent_amount?: number
          security_deposit?: number
          signed_at?: string | null
          status?: string
          tenant_id?: string | null
          tenant_signature?: string | null
          updated_at?: string
          witness_signature?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "rental_agreements_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      rental_applications: {
        Row: {
          application_data: Json
          created_at: string
          id: string
          property_id: string | null
          status: string
          submitted_at: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          application_data?: Json
          created_at?: string
          id?: string
          property_id?: string | null
          status?: string
          submitted_at?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          application_data?: Json
          created_at?: string
          id?: string
          property_id?: string | null
          status?: string
          submitted_at?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "rental_applications_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      roles: {
        Row: {
          created_at: string | null
          description: string | null
          id: number
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: number
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: number
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      saved_properties: {
        Row: {
          created_at: string
          id: string
          property_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          property_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          property_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "saved_properties_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      saved_searches: {
        Row: {
          alert_frequency: string | null
          created_at: string
          id: string
          is_active: boolean | null
          last_alert_sent: string | null
          search_criteria: Json
          search_name: string
          updated_at: string
          user_id: string
        }
        Insert: {
          alert_frequency?: string | null
          created_at?: string
          id?: string
          is_active?: boolean | null
          last_alert_sent?: string | null
          search_criteria: Json
          search_name: string
          updated_at?: string
          user_id: string
        }
        Update: {
          alert_frequency?: string | null
          created_at?: string
          id?: string
          is_active?: boolean | null
          last_alert_sent?: string | null
          search_criteria?: Json
          search_name?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      system_analytics: {
        Row: {
          additional_data: Json | null
          created_at: string
          id: string
          metric_date: string
          metric_name: string
          metric_value: number
        }
        Insert: {
          additional_data?: Json | null
          created_at?: string
          id?: string
          metric_date?: string
          metric_name: string
          metric_value: number
        }
        Update: {
          additional_data?: Json | null
          created_at?: string
          id?: string
          metric_date?: string
          metric_name?: string
          metric_value?: number
        }
        Relationships: []
      }
      tenant_history: {
        Row: {
          created_at: string
          id: string
          landlord_id: string | null
          landlord_reference: string | null
          lease_compliance_rating: number | null
          payment_history_rating: number | null
          property_care_rating: number | null
          property_id: string | null
          reason_for_leaving: string | null
          rent_amount: number
          rental_period_end: string | null
          rental_period_start: string
          user_id: string
          would_rent_again: boolean | null
        }
        Insert: {
          created_at?: string
          id?: string
          landlord_id?: string | null
          landlord_reference?: string | null
          lease_compliance_rating?: number | null
          payment_history_rating?: number | null
          property_care_rating?: number | null
          property_id?: string | null
          reason_for_leaving?: string | null
          rent_amount: number
          rental_period_end?: string | null
          rental_period_start: string
          user_id: string
          would_rent_again?: boolean | null
        }
        Update: {
          created_at?: string
          id?: string
          landlord_id?: string | null
          landlord_reference?: string | null
          lease_compliance_rating?: number | null
          payment_history_rating?: number | null
          property_care_rating?: number | null
          property_id?: string | null
          reason_for_leaving?: string | null
          rent_amount?: number
          rental_period_end?: string | null
          rental_period_start?: string
          user_id?: string
          would_rent_again?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "tenant_history_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      user_roles: {
        Row: {
          assigned_at: string | null
          assigned_by: string | null
          id: string
          role: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Insert: {
          assigned_at?: string | null
          assigned_by?: string | null
          id?: string
          role: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Update: {
          assigned_at?: string | null
          assigned_by?: string | null
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id?: string
        }
        Relationships: []
      }
      verification_documents: {
        Row: {
          application_id: string | null
          document_type: Database["public"]["Enums"]["document_type"]
          file_name: string
          file_path: string
          file_size: number | null
          id: string
          mime_type: string | null
          uploaded_at: string | null
        }
        Insert: {
          application_id?: string | null
          document_type: Database["public"]["Enums"]["document_type"]
          file_name: string
          file_path: string
          file_size?: number | null
          id?: string
          mime_type?: string | null
          uploaded_at?: string | null
        }
        Update: {
          application_id?: string | null
          document_type?: Database["public"]["Enums"]["document_type"]
          file_name?: string
          file_path?: string
          file_size?: number | null
          id?: string
          mime_type?: string | null
          uploaded_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "verification_documents_application_id_fkey"
            columns: ["application_id"]
            isOneToOne: false
            referencedRelation: "agent_applications"
            referencedColumns: ["id"]
          },
        ]
      }
      verification_status_log: {
        Row: {
          application_id: string | null
          change_reason: string | null
          changed_by: string | null
          created_at: string | null
          id: string
          new_status: Database["public"]["Enums"]["application_status"]
          notes: string | null
          previous_status:
            | Database["public"]["Enums"]["application_status"]
            | null
        }
        Insert: {
          application_id?: string | null
          change_reason?: string | null
          changed_by?: string | null
          created_at?: string | null
          id?: string
          new_status: Database["public"]["Enums"]["application_status"]
          notes?: string | null
          previous_status?:
            | Database["public"]["Enums"]["application_status"]
            | null
        }
        Update: {
          application_id?: string | null
          change_reason?: string | null
          changed_by?: string | null
          created_at?: string | null
          id?: string
          new_status?: Database["public"]["Enums"]["application_status"]
          notes?: string | null
          previous_status?:
            | Database["public"]["Enums"]["application_status"]
            | null
        }
        Relationships: [
          {
            foreignKeyName: "verification_status_log_application_id_fkey"
            columns: ["application_id"]
            isOneToOne: false
            referencedRelation: "agent_applications"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      calculate_admin_metrics: {
        Args: { admin_user_id: string; metric_date?: string }
        Returns: {
          applications_reviewed: number
          average_review_time_hours: number
          approval_rate: number
        }[]
      }
      create_agent_profile: {
        Args: { application_id: string }
        Returns: undefined
      }
      generate_agent_id: {
        Args: { applicant_name: string }
        Returns: string
      }
      get_current_user_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      has_role: {
        Args: {
          _user_id: string
          _role: Database["public"]["Enums"]["app_role"]
        }
        Returns: boolean
      }
    }
    Enums: {
      app_role: "agent" | "admin" | "super_admin"
      application_status:
        | "pending_review"
        | "documents_reviewed"
        | "referee_contacted"
        | "approved"
        | "rejected"
        | "needs_info"
      document_type: "id_document" | "selfie_with_id" | "cac_document"
      referee_status: "pending" | "contacted" | "confirmed" | "failed"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      app_role: ["agent", "admin", "super_admin"],
      application_status: [
        "pending_review",
        "documents_reviewed",
        "referee_contacted",
        "approved",
        "rejected",
        "needs_info",
      ],
      document_type: ["id_document", "selfie_with_id", "cac_document"],
      referee_status: ["pending", "contacted", "confirmed", "failed"],
    },
  },
} as const
